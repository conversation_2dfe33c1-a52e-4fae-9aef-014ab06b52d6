package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.dto.PaperGenerationRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 前端调用兼容性测试
 * 验证PaperGenerationRequest的JSON序列化/反序列化是否正确
 */
@SpringBootTest
@ActiveProfiles("test")
class PaperControllerIntegrationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    @DisplayName("测试前端JSON请求格式兼容性")
    void testFrontendJsonCompatibility() throws Exception {
        // 模拟前端发送的JSON请求
        String frontendJson = """
            {
                "title": "集合考点专项练习",
                "knowledgePointConfigs": [
                    {
                        "knowledgeId": 195,
                        "questionCount": 10,
                        "includeShortAnswer": true,
                        "shortAnswerCount": 2
                    },
                    {
                        "knowledgeId": 196,
                        "questionCount": 5,
                        "includeShortAnswer": false,
                        "shortAnswerCount": 0
                    }
                ],
                "totalScore": 53,
                "typeScoreMap": {
                    "SINGLE_CHOICE": 3,
                    "MULTIPLE_CHOICE": 3,
                    "JUDGMENT": 2,
                    "FILL_IN_BLANKS": 3,
                    "SHORT_ANSWER": 5
                },
                "difficultyCriteria": {
                    "easy": 30.0,
                    "medium": 50.0,
                    "hard": 20.0
                },
                "topicTypeCounts": {
                    "SINGLE_CHOICE": 5,
                    "MULTIPLE_CHOICE": 3,
                    "JUDGMENT": 5,
                    "FILL_IN_BLANKS": 3,
                    "SHORT_ANSWER": 2
                },
                "minReuseIntervalDays": null
            }
            """;

        // 反序列化JSON为对象
        PaperGenerationRequest request = objectMapper.readValue(frontendJson, PaperGenerationRequest.class);

        // 验证基本字段
        assertNotNull(request, "请求对象不应为空");
        assertEquals("集合考点专项练习", request.getTitle(), "标题应正确解析");
        assertEquals(53, request.getTotalScore(), "总分应正确解析");

        // 验证知识点配置
        assertNotNull(request.getKnowledgePointConfigs(), "知识点配置不应为空");
        assertEquals(2, request.getKnowledgePointConfigs().size(), "应有2个知识点配置");

        KnowledgePointConfigRequest config1 = request.getKnowledgePointConfigs().get(0);
        assertEquals(195L, config1.getKnowledgeId(), "第一个知识点ID应为195");
        assertEquals(10, config1.getQuestionCount(), "第一个知识点题目数量应为10");
        assertTrue(config1.getIncludeShortAnswer(), "第一个知识点应包含简答题");
        assertEquals(2, config1.getShortAnswerCount(), "第一个知识点简答题数量应为2");

        KnowledgePointConfigRequest config2 = request.getKnowledgePointConfigs().get(1);
        assertEquals(196L, config2.getKnowledgeId(), "第二个知识点ID应为196");
        assertEquals(5, config2.getQuestionCount(), "第二个知识点题目数量应为5");
        assertFalse(config2.getIncludeShortAnswer(), "第二个知识点不应包含简答题");
        assertEquals(0, config2.getShortAnswerCount(), "第二个知识点简答题数量应为0");

        // 验证题型分值映射
        assertNotNull(request.getTypeScoreMap(), "题型分值映射不应为空");
        assertEquals(3, request.getTypeScoreMap().get("SINGLE_CHOICE"), "单选题分值应为3");
        assertEquals(5, request.getTypeScoreMap().get("SHORT_ANSWER"), "简答题分值应为5");

        // 验证难度标准
        assertNotNull(request.getDifficultyCriteria(), "难度标准不应为空");
        assertEquals(30.0, request.getDifficultyCriteria().get("easy"), "简单题比例应为30.0");
        assertEquals(50.0, request.getDifficultyCriteria().get("medium"), "中等题比例应为50.0");
        assertEquals(20.0, request.getDifficultyCriteria().get("hard"), "困难题比例应为20.0");

        // 验证题型数量配置
        assertNotNull(request.getTopicTypeCounts(), "题型数量配置不应为空");
        assertEquals(5, request.getTopicTypeCounts().get("SINGLE_CHOICE"), "单选题数量应为5");
        assertEquals(2, request.getTopicTypeCounts().get("SHORT_ANSWER"), "简答题数量应为2");

        // 验证新添加的getKnowledgeIds方法
        List<Long> knowledgeIds = request.getKnowledgeIds();
        assertNotNull(knowledgeIds, "知识点ID列表不应为空");
        assertEquals(2, knowledgeIds.size(), "应有2个知识点ID");
        assertTrue(knowledgeIds.contains(195L), "应包含知识点ID 195");
        assertTrue(knowledgeIds.contains(196L), "应包含知识点ID 196");
    }

    @Test
    @DisplayName("测试JSON序列化兼容性")
    void testJsonSerializationCompatibility() throws Exception {
        // 创建请求对象
        PaperGenerationRequest request = new PaperGenerationRequest();
        request.setTitle("测试试卷");
        request.setTotalScore(100);

        // 创建知识点配置
        List<KnowledgePointConfigRequest> configs = new ArrayList<>();
        
        KnowledgePointConfigRequest config1 = new KnowledgePointConfigRequest();
        config1.setKnowledgeId(1L);
        config1.setQuestionCount(10);
        config1.setIncludeShortAnswer(true);
        config1.setShortAnswerCount(2);
        configs.add(config1);

        KnowledgePointConfigRequest config2 = new KnowledgePointConfigRequest();
        config2.setKnowledgeId(2L);
        config2.setQuestionCount(5);
        config2.setIncludeShortAnswer(false);
        config2.setShortAnswerCount(0);
        configs.add(config2);

        request.setKnowledgePointConfigs(configs);

        // 设置题型分值映射
        Map<String, Integer> typeScoreMap = new HashMap<>();
        typeScoreMap.put("SINGLE_CHOICE", 3);
        typeScoreMap.put("MULTIPLE_CHOICE", 4);
        typeScoreMap.put("SHORT_ANSWER", 5);
        request.setTypeScoreMap(typeScoreMap);

        // 设置难度标准
        Map<String, Double> difficultyCriteria = new HashMap<>();
        difficultyCriteria.put("easy", 0.3);
        difficultyCriteria.put("medium", 0.5);
        difficultyCriteria.put("hard", 0.2);
        request.setDifficultyCriteria(difficultyCriteria);

        // 设置题型数量配置
        Map<String, Integer> topicTypeCounts = new HashMap<>();
        topicTypeCounts.put("SINGLE_CHOICE", 10);
        topicTypeCounts.put("MULTIPLE_CHOICE", 5);
        topicTypeCounts.put("SHORT_ANSWER", 2);
        request.setTopicTypeCounts(topicTypeCounts);

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(request);
        assertNotNull(json, "JSON序列化结果不应为空");
        assertTrue(json.contains("knowledgePointConfigs"), "JSON应包含knowledgePointConfigs字段");
        assertTrue(json.contains("\"knowledgeId\":1"), "JSON应包含知识点ID");
        assertTrue(json.contains("\"includeShortAnswer\":true"), "JSON应包含简答题配置");

        // 反序列化验证
        PaperGenerationRequest deserializedRequest = objectMapper.readValue(json, PaperGenerationRequest.class);
        assertNotNull(deserializedRequest, "反序列化结果不应为空");
        assertEquals(request.getTitle(), deserializedRequest.getTitle(), "标题应一致");
        assertEquals(request.getTotalScore(), deserializedRequest.getTotalScore(), "总分应一致");
        assertEquals(request.getKnowledgePointConfigs().size(), 
                    deserializedRequest.getKnowledgePointConfigs().size(), "知识点配置数量应一致");

        // 验证getKnowledgeIds方法在序列化后仍然工作
        List<Long> originalIds = request.getKnowledgeIds();
        List<Long> deserializedIds = deserializedRequest.getKnowledgeIds();
        assertEquals(originalIds.size(), deserializedIds.size(), "知识点ID数量应一致");
        assertTrue(deserializedIds.containsAll(originalIds), "知识点ID应完全一致");
    }

    @Test
    @DisplayName("测试缓存键生成兼容性")
    void testCacheKeyGenerationCompatibility() {
        // 创建请求对象
        PaperGenerationRequest request = new PaperGenerationRequest();
        
        List<KnowledgePointConfigRequest> configs = new ArrayList<>();
        
        KnowledgePointConfigRequest config1 = new KnowledgePointConfigRequest();
        config1.setKnowledgeId(195L);
        config1.setQuestionCount(10);
        configs.add(config1);

        KnowledgePointConfigRequest config2 = new KnowledgePointConfigRequest();
        config2.setKnowledgeId(196L);
        config2.setQuestionCount(5);
        configs.add(config2);

        request.setKnowledgePointConfigs(configs);

        // 验证getKnowledgeIds方法返回正确的ID列表
        List<Long> knowledgeIds = request.getKnowledgeIds();
        assertNotNull(knowledgeIds, "知识点ID列表不应为空");
        assertEquals(2, knowledgeIds.size(), "应有2个知识点ID");
        assertTrue(knowledgeIds.contains(195L), "应包含知识点ID 195");
        assertTrue(knowledgeIds.contains(196L), "应包含知识点ID 196");

        // 验证toString方法可以用于缓存键生成
        String knowledgeIdsString = knowledgeIds.toString();
        assertNotNull(knowledgeIdsString, "知识点ID字符串不应为空");
        assertTrue(knowledgeIdsString.contains("195"), "字符串应包含195");
        assertTrue(knowledgeIdsString.contains("196"), "字符串应包含196");
    }

    @Test
    @DisplayName("测试认知层次标准默认值")
    void testCognitiveLevelCriteriaDefaults() {
        PaperGenerationRequest request = new PaperGenerationRequest();
        
        Map<String, Double> criteria = request.getCognitiveLevelCriteria();
        assertNotNull(criteria, "认知层次标准不应为空");
        assertEquals(3, criteria.size(), "应有3个认知层次");
        
        // 验证默认值
        assertEquals(0.3, criteria.get("remember"), "remember层次默认值应为0.3");
        assertEquals(0.3, criteria.get("understand"), "understand层次默认值应为0.3");
        assertEquals(0.4, criteria.get("apply"), "apply层次默认值应为0.4");
        
        // 验证总和为1.0
        double sum = criteria.values().stream().mapToDouble(Double::doubleValue).sum();
        assertEquals(1.0, sum, 0.001, "认知层次比例总和应为1.0");
    }
}
