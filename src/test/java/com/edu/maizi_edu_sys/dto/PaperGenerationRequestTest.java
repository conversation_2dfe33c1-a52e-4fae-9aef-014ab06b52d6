package com.edu.maizi_edu_sys.dto;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * PaperGenerationRequest 测试类
 * 验证新添加的 getKnowledgeIds 方法
 */
class PaperGenerationRequestTest {

    @Test
    @DisplayName("测试getKnowledgeIds方法 - 正常情况")
    void testGetKnowledgeIds_Normal() {
        // 创建测试数据
        PaperGenerationRequest request = new PaperGenerationRequest();
        
        List<KnowledgePointConfigRequest> configs = new ArrayList<>();
        
        KnowledgePointConfigRequest config1 = new KnowledgePointConfigRequest();
        config1.setKnowledgeId(1L);
        config1.setQuestionCount(5);
        config1.setIncludeShortAnswer(false);
        configs.add(config1);
        
        KnowledgePointConfigRequest config2 = new KnowledgePointConfigRequest();
        config2.setKnowledgeId(2L);
        config2.setQuestionCount(3);
        config2.setIncludeShortAnswer(true);
        configs.add(config2);
        
        KnowledgePointConfigRequest config3 = new KnowledgePointConfigRequest();
        config3.setKnowledgeId(3L);
        config3.setQuestionCount(7);
        config3.setIncludeShortAnswer(false);
        configs.add(config3);
        
        request.setKnowledgePointConfigs(configs);
        
        // 执行测试
        List<Long> knowledgeIds = request.getKnowledgeIds();
        
        // 验证结果
        assertNotNull(knowledgeIds, "知识点ID列表不应为空");
        assertEquals(3, knowledgeIds.size(), "应该有3个知识点ID");
        assertTrue(knowledgeIds.contains(1L), "应该包含知识点ID 1");
        assertTrue(knowledgeIds.contains(2L), "应该包含知识点ID 2");
        assertTrue(knowledgeIds.contains(3L), "应该包含知识点ID 3");
    }

    @Test
    @DisplayName("测试getKnowledgeIds方法 - 空配置列表")
    void testGetKnowledgeIds_EmptyConfigs() {
        PaperGenerationRequest request = new PaperGenerationRequest();
        request.setKnowledgePointConfigs(new ArrayList<>());
        
        List<Long> knowledgeIds = request.getKnowledgeIds();
        
        assertNotNull(knowledgeIds, "知识点ID列表不应为空");
        assertTrue(knowledgeIds.isEmpty(), "空配置列表应返回空的知识点ID列表");
    }

    @Test
    @DisplayName("测试getKnowledgeIds方法 - null配置列表")
    void testGetKnowledgeIds_NullConfigs() {
        PaperGenerationRequest request = new PaperGenerationRequest();
        request.setKnowledgePointConfigs(null);
        
        List<Long> knowledgeIds = request.getKnowledgeIds();
        
        assertNotNull(knowledgeIds, "知识点ID列表不应为空");
        assertTrue(knowledgeIds.isEmpty(), "null配置列表应返回空的知识点ID列表");
    }

    @Test
    @DisplayName("测试getKnowledgeIds方法 - 包含null知识点ID")
    void testGetKnowledgeIds_WithNullKnowledgeId() {
        PaperGenerationRequest request = new PaperGenerationRequest();
        
        List<KnowledgePointConfigRequest> configs = new ArrayList<>();
        
        KnowledgePointConfigRequest config1 = new KnowledgePointConfigRequest();
        config1.setKnowledgeId(1L);
        config1.setQuestionCount(5);
        configs.add(config1);
        
        KnowledgePointConfigRequest config2 = new KnowledgePointConfigRequest();
        config2.setKnowledgeId(null); // null知识点ID
        config2.setQuestionCount(3);
        configs.add(config2);
        
        KnowledgePointConfigRequest config3 = new KnowledgePointConfigRequest();
        config3.setKnowledgeId(3L);
        config3.setQuestionCount(7);
        configs.add(config3);
        
        request.setKnowledgePointConfigs(configs);
        
        List<Long> knowledgeIds = request.getKnowledgeIds();
        
        assertNotNull(knowledgeIds, "知识点ID列表不应为空");
        assertEquals(2, knowledgeIds.size(), "应该过滤掉null知识点ID，只有2个有效ID");
        assertTrue(knowledgeIds.contains(1L), "应该包含知识点ID 1");
        assertTrue(knowledgeIds.contains(3L), "应该包含知识点ID 3");
        assertFalse(knowledgeIds.contains(null), "不应该包含null值");
    }

    @Test
    @DisplayName("测试getKnowledgeIds方法 - 重复的知识点ID")
    void testGetKnowledgeIds_DuplicateIds() {
        PaperGenerationRequest request = new PaperGenerationRequest();
        
        List<KnowledgePointConfigRequest> configs = new ArrayList<>();
        
        KnowledgePointConfigRequest config1 = new KnowledgePointConfigRequest();
        config1.setKnowledgeId(1L);
        config1.setQuestionCount(5);
        configs.add(config1);
        
        KnowledgePointConfigRequest config2 = new KnowledgePointConfigRequest();
        config2.setKnowledgeId(1L); // 重复的知识点ID
        config2.setQuestionCount(3);
        configs.add(config2);
        
        KnowledgePointConfigRequest config3 = new KnowledgePointConfigRequest();
        config3.setKnowledgeId(2L);
        config3.setQuestionCount(7);
        configs.add(config3);
        
        request.setKnowledgePointConfigs(configs);
        
        List<Long> knowledgeIds = request.getKnowledgeIds();
        
        assertNotNull(knowledgeIds, "知识点ID列表不应为空");
        assertEquals(3, knowledgeIds.size(), "应该保留所有配置，包括重复的ID");
        
        // 验证包含重复的ID
        long count1 = knowledgeIds.stream().filter(id -> id.equals(1L)).count();
        long count2 = knowledgeIds.stream().filter(id -> id.equals(2L)).count();
        
        assertEquals(2, count1, "知识点ID 1应该出现2次");
        assertEquals(1, count2, "知识点ID 2应该出现1次");
    }

    @Test
    @DisplayName("测试getCognitiveLevelCriteria方法 - 默认值")
    void testGetCognitiveLevelCriteria_DefaultValues() {
        PaperGenerationRequest request = new PaperGenerationRequest();
        
        var criteria = request.getCognitiveLevelCriteria();
        
        assertNotNull(criteria, "认知层次标准不应为空");
        assertEquals(3, criteria.size(), "应该有3个认知层次");
        assertEquals(0.3, criteria.get("remember"), "remember层次应为0.3");
        assertEquals(0.3, criteria.get("understand"), "understand层次应为0.3");
        assertEquals(0.4, criteria.get("apply"), "apply层次应为0.4");
        
        // 验证总和为1.0
        double sum = criteria.values().stream().mapToDouble(Double::doubleValue).sum();
        assertEquals(1.0, sum, 0.001, "认知层次比例总和应为1.0");
    }
}
