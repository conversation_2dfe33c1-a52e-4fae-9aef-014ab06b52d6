package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.dto.KnowledgePointConfigRequest;
import com.edu.maizi_edu_sys.dto.PaperGenerationRequest;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;

/**
 * 简单的JSON兼容性测试
 * 验证前端调用的兼容性
 */
public class JsonCompatibilityTest {
    
    public static void main(String[] args) {
        try {
            testJsonCompatibility();
            System.out.println("✅ 所有测试通过！前端调用兼容性正常。");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testJsonCompatibility() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 1. 测试前端JSON格式解析
        System.out.println("🔍 测试1: 前端JSON格式解析");
        String frontendJson = """
            {
                "title": "集合考点专项练习",
                "knowledgePointConfigs": [
                    {
                        "knowledgeId": 195,
                        "questionCount": 10,
                        "includeShortAnswer": true,
                        "shortAnswerCount": 2
                    },
                    {
                        "knowledgeId": 196,
                        "questionCount": 5,
                        "includeShortAnswer": false,
                        "shortAnswerCount": 0
                    }
                ],
                "totalScore": 53,
                "typeScoreMap": {
                    "SINGLE_CHOICE": 3,
                    "MULTIPLE_CHOICE": 3,
                    "JUDGMENT": 2,
                    "FILL_IN_BLANKS": 3,
                    "SHORT_ANSWER": 5
                },
                "difficultyCriteria": {
                    "easy": 30.0,
                    "medium": 50.0,
                    "hard": 20.0
                },
                "topicTypeCounts": {
                    "SINGLE_CHOICE": 5,
                    "MULTIPLE_CHOICE": 3,
                    "JUDGMENT": 5,
                    "FILL_IN_BLANKS": 3,
                    "SHORT_ANSWER": 2
                }
            }
            """;
        
        PaperGenerationRequest request = objectMapper.readValue(frontendJson, PaperGenerationRequest.class);
        assert request != null : "请求对象不应为空";
        assert "集合考点专项练习".equals(request.getTitle()) : "标题解析错误";
        assert request.getTotalScore() == 53 : "总分解析错误";
        assert request.getKnowledgePointConfigs().size() == 2 : "知识点配置数量错误";
        System.out.println("✅ 前端JSON格式解析正常");
        
        // 2. 测试getKnowledgeIds方法
        System.out.println("🔍 测试2: getKnowledgeIds方法");
        List<Long> knowledgeIds = request.getKnowledgeIds();
        assert knowledgeIds != null : "知识点ID列表不应为空";
        assert knowledgeIds.size() == 2 : "知识点ID数量错误";
        assert knowledgeIds.contains(195L) : "应包含知识点ID 195";
        assert knowledgeIds.contains(196L) : "应包含知识点ID 196";
        System.out.println("✅ getKnowledgeIds方法工作正常，返回: " + knowledgeIds);
        
        // 3. 测试缓存键生成
        System.out.println("🔍 测试3: 缓存键生成");
        String cacheKeyPart = knowledgeIds.toString();
        assert cacheKeyPart.contains("195") : "缓存键应包含195";
        assert cacheKeyPart.contains("196") : "缓存键应包含196";
        System.out.println("✅ 缓存键生成正常: " + cacheKeyPart);
        
        // 4. 测试知识点配置详细信息
        System.out.println("🔍 测试4: 知识点配置详细信息");
        KnowledgePointConfigRequest config1 = request.getKnowledgePointConfigs().get(0);
        assert config1.getKnowledgeId().equals(195L) : "第一个知识点ID错误";
        assert config1.getQuestionCount() == 10 : "第一个知识点题目数量错误";
        assert config1.getIncludeShortAnswer() : "第一个知识点应包含简答题";
        assert config1.getShortAnswerCount() == 2 : "第一个知识点简答题数量错误";
        
        KnowledgePointConfigRequest config2 = request.getKnowledgePointConfigs().get(1);
        assert config2.getKnowledgeId().equals(196L) : "第二个知识点ID错误";
        assert config2.getQuestionCount() == 5 : "第二个知识点题目数量错误";
        assert !config2.getIncludeShortAnswer() : "第二个知识点不应包含简答题";
        assert config2.getShortAnswerCount() == 0 : "第二个知识点简答题数量错误";
        System.out.println("✅ 知识点配置详细信息正常");
        
        // 5. 测试序列化兼容性
        System.out.println("🔍 测试5: 序列化兼容性");
        String serializedJson = objectMapper.writeValueAsString(request);
        assert serializedJson.contains("knowledgePointConfigs") : "序列化结果应包含knowledgePointConfigs";
        assert serializedJson.contains("\"knowledgeId\":195") : "序列化结果应包含知识点ID";
        
        PaperGenerationRequest deserializedRequest = objectMapper.readValue(serializedJson, PaperGenerationRequest.class);
        List<Long> deserializedIds = deserializedRequest.getKnowledgeIds();
        assert deserializedIds.size() == knowledgeIds.size() : "反序列化后知识点ID数量应一致";
        assert deserializedIds.containsAll(knowledgeIds) : "反序列化后知识点ID应完全一致";
        System.out.println("✅ 序列化兼容性正常");
        
        // 6. 测试边界情况
        System.out.println("🔍 测试6: 边界情况");
        
        // 空知识点配置
        PaperGenerationRequest emptyRequest = new PaperGenerationRequest();
        emptyRequest.setKnowledgePointConfigs(new ArrayList<>());
        List<Long> emptyIds = emptyRequest.getKnowledgeIds();
        assert emptyIds != null : "空配置的知识点ID列表不应为null";
        assert emptyIds.isEmpty() : "空配置的知识点ID列表应为空";
        
        // null知识点配置
        PaperGenerationRequest nullRequest = new PaperGenerationRequest();
        nullRequest.setKnowledgePointConfigs(null);
        List<Long> nullIds = nullRequest.getKnowledgeIds();
        assert nullIds != null : "null配置的知识点ID列表不应为null";
        assert nullIds.isEmpty() : "null配置的知识点ID列表应为空";
        
        // 包含null知识点ID的配置
        PaperGenerationRequest mixedRequest = new PaperGenerationRequest();
        List<KnowledgePointConfigRequest> mixedConfigs = new ArrayList<>();
        
        KnowledgePointConfigRequest validConfig = new KnowledgePointConfigRequest();
        validConfig.setKnowledgeId(1L);
        mixedConfigs.add(validConfig);
        
        KnowledgePointConfigRequest nullIdConfig = new KnowledgePointConfigRequest();
        nullIdConfig.setKnowledgeId(null);
        mixedConfigs.add(nullIdConfig);
        
        mixedRequest.setKnowledgePointConfigs(mixedConfigs);
        List<Long> mixedIds = mixedRequest.getKnowledgeIds();
        assert mixedIds.size() == 1 : "应过滤掉null知识点ID";
        assert mixedIds.contains(1L) : "应保留有效的知识点ID";
        assert !mixedIds.contains(null) : "不应包含null值";
        
        System.out.println("✅ 边界情况处理正常");
        
        // 7. 测试认知层次标准默认值
        System.out.println("🔍 测试7: 认知层次标准默认值");
        Map<String, Double> criteria = request.getCognitiveLevelCriteria();
        assert criteria != null : "认知层次标准不应为空";
        assert criteria.size() == 3 : "应有3个认知层次";
        assert criteria.get("remember") == 0.3 : "remember层次默认值错误";
        assert criteria.get("understand") == 0.3 : "understand层次默认值错误";
        assert criteria.get("apply") == 0.4 : "apply层次默认值错误";
        
        double sum = criteria.values().stream().mapToDouble(Double::doubleValue).sum();
        assert Math.abs(sum - 1.0) < 0.001 : "认知层次比例总和应为1.0";
        System.out.println("✅ 认知层次标准默认值正常");
    }
}
