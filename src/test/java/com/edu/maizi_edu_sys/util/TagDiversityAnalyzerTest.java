package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.entity.Topic;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TagDiversityAnalyzer 测试类
 * 验证标签多样性分析的核心功能
 */
@SpringBootTest
class TagDiversityAnalyzerTest {

    @Autowired
    private TagDiversityAnalyzer tagDiversityAnalyzer;

    private List<Topic> testTopics;

    @BeforeEach
    void setUp() {
        testTopics = createTestTopicsWithTags();
    }

    @Test
    @DisplayName("测试TagDiversityAnalyzer是否正确注入")
    void testTagDiversityAnalyzerInjection() {
        assertNotNull(tagDiversityAnalyzer, "TagDiversityAnalyzer应该被正确注入");
    }

    @Test
    @DisplayName("测试标签解析功能")
    void testParseTopicTags() {
        Topic topic = new Topic();
        topic.setTags("信息技术必修1数据与计算-第1章-认识数据与大数据-数据、信息与知识,信息技术必修1数据与计算-第1章-认识数据与大数据-数字化与编码");
        
        List<String> tags = tagDiversityAnalyzer.parseTopicTags(topic);
        
        assertNotNull(tags, "标签列表不应为空");
        assertEquals(2, tags.size(), "应该解析出2个标签");
        assertTrue(tags.contains("信息技术必修1数据与计算-第1章-认识数据与大数据-数据、信息与知识"));
        assertTrue(tags.contains("信息技术必修1数据与计算-第1章-认识数据与大数据-数字化与编码"));
    }

    @Test
    @DisplayName("测试空标签处理")
    void testEmptyTags() {
        Topic topic = new Topic();
        topic.setTags("");
        
        List<String> tags = tagDiversityAnalyzer.parseTopicTags(topic);
        
        assertNotNull(tags, "标签列表不应为空");
        assertTrue(tags.isEmpty(), "空标签应返回空列表");
    }

    @Test
    @DisplayName("测试标签多样性计算")
    void testCalculateTagDiversity() {
        double diversity = tagDiversityAnalyzer.calculateTagDiversity(testTopics);
        
        assertTrue(diversity >= 0.0 && diversity <= 1.0, 
                  "多样性分数应在[0,1]范围内，实际值: " + diversity);
        
        // 测试空列表
        double emptyDiversity = tagDiversityAnalyzer.calculateTagDiversity(new ArrayList<>());
        assertEquals(0.0, emptyDiversity, "空列表的多样性应为0");
    }

    @Test
    @DisplayName("测试标签分布集中度检测")
    void testIsTagDistributionTooConcentrated() {
        // 测试正常分布（不应过于集中）
        boolean concentrated = tagDiversityAnalyzer.isTagDistributionTooConcentrated(testTopics, 0.5);
        assertFalse(concentrated, "测试数据的标签分布不应过于集中");
        
        // 测试高度集中的情况
        List<Topic> concentratedTopics = createConcentratedTopics();
        boolean shouldBeConcentrated = tagDiversityAnalyzer.isTagDistributionTooConcentrated(concentratedTopics, 0.5);
        assertTrue(shouldBeConcentrated, "高度集中的标签分布应被检测出来");
    }

    @Test
    @DisplayName("测试标签分布统计分析")
    void testAnalyzeTagDistribution() {
        TagDiversityAnalyzer.TagDistributionStats stats = 
            tagDiversityAnalyzer.analyzeTagDistribution(testTopics);
        
        assertNotNull(stats, "统计结果不应为空");
        assertTrue(stats.getTotalTopics() > 0, "总题目数应大于0");
        assertTrue(stats.getTopicsWithTags() >= 0, "有标签的题目数应大于等于0");
        assertTrue(stats.getDiversity() >= 0.0 && stats.getDiversity() <= 1.0, 
                  "多样性分数应在[0,1]范围内");
        assertTrue(stats.getTagCoverage() >= 0.0 && stats.getTagCoverage() <= 1.0, 
                  "标签覆盖率应在[0,1]范围内");
        assertNotNull(stats.getTagCounts(), "标签计数不应为空");
    }

    @Test
    @DisplayName("测试标签层级解析")
    void testParseTagHierarchy() {
        String tag = "信息技术必修1数据与计算-第1章-认识数据与大数据-数据、信息与知识";
        
        TagDiversityAnalyzer.TagHierarchy hierarchy = tagDiversityAnalyzer.parseTagHierarchy(tag);
        
        assertNotNull(hierarchy, "层级信息不应为空");
        assertEquals("信息技术必修1数据与计算", hierarchy.getBook(), "书籍名称解析错误");
        assertEquals("第1章", hierarchy.getChapter(), "章节解析错误");
        assertEquals("认识数据与大数据", hierarchy.getChapterName(), "章节名称解析错误");
        assertEquals("数据、信息与知识", hierarchy.getSection(), "小节解析错误");
    }

    @Test
    @DisplayName("测试标签相似度计算")
    void testCalculateTagSimilarity() {
        String tag1 = "信息技术必修1数据与计算-第1章-认识数据与大数据-数据、信息与知识";
        String tag2 = "信息技术必修1数据与计算-第1章-认识数据与大数据-数字化与编码";
        String tag3 = "信息技术必修1数据与计算-第2章-算法与程序实现-算法的概念及描述";
        
        double similarity1 = tagDiversityAnalyzer.calculateTagSimilarity(tag1, tag2);
        double similarity2 = tagDiversityAnalyzer.calculateTagSimilarity(tag1, tag3);
        
        assertTrue(similarity1 > similarity2, 
                  "同一章节下的标签相似度应该更高");
        assertTrue(similarity1 >= 0.0 && similarity1 <= 1.0, 
                  "相似度应在[0,1]范围内");
        assertTrue(similarity2 >= 0.0 && similarity2 <= 1.0, 
                  "相似度应在[0,1]范围内");
    }

    /**
     * 创建测试用的题目数据，包含多样化的标签
     */
    private List<Topic> createTestTopicsWithTags() {
        List<Topic> topics = new ArrayList<>();
        
        // 第一章的题目
        for (int i = 1; i <= 5; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setTags("信息技术必修1数据与计算-第1章-认识数据与大数据-数据、信息与知识");
            topics.add(topic);
        }
        
        // 第二章的题目
        for (int i = 6; i <= 10; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setTags("信息技术必修1数据与计算-第2章-算法与程序实现-算法的概念及描述");
            topics.add(topic);
        }
        
        // 第三章的题目
        for (int i = 11; i <= 15; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setTags("信息技术必修1数据与计算-第3章-数据处理与应用-数据处理的一般过程");
            topics.add(topic);
        }
        
        return topics;
    }

    /**
     * 创建高度集中的题目数据（大部分题目来自同一标签）
     */
    private List<Topic> createConcentratedTopics() {
        List<Topic> topics = new ArrayList<>();
        
        // 90%的题目来自同一标签
        for (int i = 1; i <= 18; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setTags("信息技术必修1数据与计算-第1章-认识数据与大数据-数据、信息与知识");
            topics.add(topic);
        }
        
        // 10%的题目来自其他标签
        for (int i = 19; i <= 20; i++) {
            Topic topic = new Topic();
            topic.setId(i);
            topic.setTags("信息技术必修1数据与计算-第2章-算法与程序实现-算法的概念及描述");
            topics.add(topic);
        }
        
        return topics;
    }
}
