package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.entity.Topic;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TopicTypeMapper 集成测试
 * 验证题型映射在各个组件中的一致性
 */
class TopicTypeMapperIntegrationTest {

    @Test
    @DisplayName("测试题型映射的一致性")
    void testTopicTypeMappingConsistency() {
        // 测试各种输入格式都能正确映射到数据库格式
        
        // 前端格式
        assertEquals("choice", TopicTypeMapper.toDbFormat("SINGLE_CHOICE"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("MULTIPLE_CHOICE"));
        assertEquals("judge", TopicTypeMapper.toDbFormat("JUDGE"));
        assertEquals("fill", TopicTypeMapper.toDbFormat("FILL"));
        assertEquals("short", TopicTypeMapper.toDbFormat("SHORT"));
        
        // 数据库格式（应该保持不变）
        assertEquals("choice", TopicTypeMapper.toDbFormat("choice"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("multiple"));
        assertEquals("judge", TopicTypeMapper.toDbFormat("judge"));
        assertEquals("fill", TopicTypeMapper.toDbFormat("fill"));
        assertEquals("short", TopicTypeMapper.toDbFormat("short"));
        
        // 驼峰格式
        assertEquals("choice", TopicTypeMapper.toDbFormat("singleChoice"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("multipleChoice"));
        assertEquals("judge", TopicTypeMapper.toDbFormat("judgment"));
        assertEquals("fill", TopicTypeMapper.toDbFormat("fillBlank"));
        assertEquals("short", TopicTypeMapper.toDbFormat("shortAnswer"));
    }

    @Test
    @DisplayName("测试中文名称映射")
    void testChineseNameMapping() {
        assertEquals("单选题", TopicTypeMapper.getChineseName("choice"));
        assertEquals("多选题", TopicTypeMapper.getChineseName("multiple"));
        assertEquals("判断题", TopicTypeMapper.getChineseName("judge"));
        assertEquals("填空题", TopicTypeMapper.getChineseName("fill"));
        assertEquals("简答题", TopicTypeMapper.getChineseName("short"));
        assertEquals("主观题", TopicTypeMapper.getChineseName("subjective"));
        assertEquals("组合题", TopicTypeMapper.getChineseName("group"));
    }

    @Test
    @DisplayName("测试显示名称获取")
    void testDisplayNameRetrieval() {
        // 测试从各种格式获取显示名称
        assertEquals("单选题", TopicTypeMapper.getDisplayName("SINGLE_CHOICE"));
        assertEquals("多选题", TopicTypeMapper.getDisplayName("MULTIPLE_CHOICE"));
        assertEquals("判断题", TopicTypeMapper.getDisplayName("JUDGE"));
        assertEquals("填空题", TopicTypeMapper.getDisplayName("FILL"));
        assertEquals("简答题", TopicTypeMapper.getDisplayName("SHORT"));
        
        assertEquals("单选题", TopicTypeMapper.getDisplayName("choice"));
        assertEquals("多选题", TopicTypeMapper.getDisplayName("multiple"));
        assertEquals("判断题", TopicTypeMapper.getDisplayName("judge"));
        assertEquals("填空题", TopicTypeMapper.getDisplayName("fill"));
        assertEquals("简答题", TopicTypeMapper.getDisplayName("short"));
    }

    @Test
    @DisplayName("测试题型验证")
    void testTopicTypeValidation() {
        // 有效题型
        assertTrue(TopicTypeMapper.isValidType("SINGLE_CHOICE"));
        assertTrue(TopicTypeMapper.isValidType("choice"));
        assertTrue(TopicTypeMapper.isValidType("singleChoice"));
        
        // 无效题型
        assertFalse(TopicTypeMapper.isValidType("invalid_type"));
        assertFalse(TopicTypeMapper.isValidType(""));
        assertFalse(TopicTypeMapper.isValidType(null));
    }

    @Test
    @DisplayName("测试题型分组功能")
    void testTopicGroupingByType() {
        // 创建测试题目
        List<Topic> topics = Arrays.asList(
            createTopic(1, "SINGLE_CHOICE"),
            createTopic(2, "choice"),
            createTopic(3, "singleChoice"),
            createTopic(4, "MULTIPLE_CHOICE"),
            createTopic(5, "multiple"),
            createTopic(6, "JUDGE"),
            createTopic(7, "judge"),
            createTopic(8, "FILL"),
            createTopic(9, "fill"),
            createTopic(10, "SHORT"),
            createTopic(11, "short")
        );

        // 按标准化题型分组
        Map<String, List<Topic>> groupedTopics = topics.stream()
            .collect(Collectors.groupingBy(
                topic -> TopicTypeMapper.toDbFormat(topic.getType())
            ));

        // 验证分组结果
        assertEquals(5, groupedTopics.size(), "应该有5种不同的题型");
        assertEquals(3, groupedTopics.get("choice").size(), "单选题应该有3个");
        assertEquals(2, groupedTopics.get("multiple").size(), "多选题应该有2个");
        assertEquals(2, groupedTopics.get("judge").size(), "判断题应该有2个");
        assertEquals(2, groupedTopics.get("fill").size(), "填空题应该有2个");
        assertEquals(2, groupedTopics.get("short").size(), "简答题应该有2个");
    }

    @Test
    @DisplayName("测试前端格式转换")
    void testFrontendFormatConversion() {
        // 测试各种格式转换为前端格式
        assertEquals("SINGLE_CHOICE", TopicTypeMapper.toFrontendFormat("choice"));
        assertEquals("MULTIPLE_CHOICE", TopicTypeMapper.toFrontendFormat("multiple"));
        assertEquals("JUDGE", TopicTypeMapper.toFrontendFormat("judge"));
        assertEquals("FILL", TopicTypeMapper.toFrontendFormat("fill"));
        assertEquals("SHORT", TopicTypeMapper.toFrontendFormat("short"));
        
        // 测试前端格式保持不变
        assertEquals("SINGLE_CHOICE", TopicTypeMapper.toFrontendFormat("SINGLE_CHOICE"));
        assertEquals("MULTIPLE_CHOICE", TopicTypeMapper.toFrontendFormat("MULTIPLE_CHOICE"));
    }

    @Test
    @DisplayName("测试边界情况处理")
    void testEdgeCases() {
        // null 值处理
        assertEquals("choice", TopicTypeMapper.toDbFormat(null));
        assertEquals("SINGLE_CHOICE", TopicTypeMapper.toFrontendFormat(null));
        assertEquals("未知题型", TopicTypeMapper.getDisplayName(null));
        
        // 空字符串处理
        assertEquals("choice", TopicTypeMapper.toDbFormat(""));
        assertEquals("choice", TopicTypeMapper.toDbFormat("   "));
        
        // 未知题型处理
        assertEquals("choice", TopicTypeMapper.toDbFormat("unknown_type"));
        assertTrue(TopicTypeMapper.getDisplayName("unknown_type").contains("unknown_type"));
    }

    @Test
    @DisplayName("测试大小写不敏感")
    void testCaseInsensitive() {
        // 测试大小写不敏感
        assertEquals("choice", TopicTypeMapper.toDbFormat("CHOICE"));
        assertEquals("choice", TopicTypeMapper.toDbFormat("Choice"));
        assertEquals("choice", TopicTypeMapper.toDbFormat("choice"));
        assertEquals("choice", TopicTypeMapper.toDbFormat("cHoIcE"));
        
        assertEquals("multiple", TopicTypeMapper.toDbFormat("MULTIPLE"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("Multiple"));
        assertEquals("multiple", TopicTypeMapper.toDbFormat("multiple"));
    }

    @Test
    @DisplayName("测试所有支持的题型")
    void testAllSupportedTypes() {
        String[] dbTypes = TopicTypeMapper.getAllDbTypes();
        String[] frontendTypes = TopicTypeMapper.getAllFrontendTypes();
        String[] camelTypes = TopicTypeMapper.getAllCamelTypes();
        
        assertEquals(7, dbTypes.length, "数据库格式题型数量应该是7");
        assertEquals(7, frontendTypes.length, "前端格式题型数量应该是7");
        assertEquals(7, camelTypes.length, "驼峰格式题型数量应该是7");
        
        // 验证每个数据库题型都有对应的前端格式和驼峰格式
        for (String dbType : dbTypes) {
            String frontendType = TopicTypeMapper.toFrontendFormat(dbType);
            assertNotNull(frontendType, "数据库题型 " + dbType + " 应该有对应的前端格式");
            
            String camelType = TopicTypeMapper.normalize(dbType);
            assertNotNull(camelType, "数据库题型 " + dbType + " 应该有对应的驼峰格式");
            
            // 验证往返转换的一致性
            assertEquals(dbType, TopicTypeMapper.toDbFormat(frontendType), 
                        "前端格式 " + frontendType + " 应该能正确转换回数据库格式");
            assertEquals(dbType, TopicTypeMapper.toDbFormat(camelType), 
                        "驼峰格式 " + camelType + " 应该能正确转换回数据库格式");
        }
    }

    @Test
    @DisplayName("测试常量定义的一致性")
    void testConstantConsistency() {
        // 验证常量定义的一致性
        assertEquals("choice", TopicTypeMapper.DB_SINGLE_CHOICE);
        assertEquals("multiple", TopicTypeMapper.DB_MULTIPLE_CHOICE);
        assertEquals("judge", TopicTypeMapper.DB_JUDGMENT);
        assertEquals("fill", TopicTypeMapper.DB_FILL_BLANK);
        assertEquals("short", TopicTypeMapper.DB_SHORT_ANSWER);
        assertEquals("subjective", TopicTypeMapper.DB_SUBJECTIVE);
        assertEquals("group", TopicTypeMapper.DB_GROUP);
        
        assertEquals("SINGLE_CHOICE", TopicTypeMapper.FRONTEND_SINGLE_CHOICE);
        assertEquals("MULTIPLE_CHOICE", TopicTypeMapper.FRONTEND_MULTIPLE_CHOICE);
        assertEquals("JUDGE", TopicTypeMapper.FRONTEND_JUDGMENT);
        assertEquals("FILL", TopicTypeMapper.FRONTEND_FILL_BLANK);
        assertEquals("SHORT", TopicTypeMapper.FRONTEND_SHORT_ANSWER);
        assertEquals("SUBJECTIVE", TopicTypeMapper.FRONTEND_SUBJECTIVE);
        assertEquals("GROUP", TopicTypeMapper.FRONTEND_GROUP);
    }

    /**
     * 创建测试用的题目对象
     */
    private Topic createTopic(int id, String type) {
        Topic topic = new Topic();
        topic.setId(id);
        topic.setType(type);
        topic.setScore(5);
        return topic;
    }
}
