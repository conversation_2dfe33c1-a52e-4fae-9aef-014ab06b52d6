package com.edu.maizi_edu_sys.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis配置测试类
 * 验证Redis连接和RedisTemplate bean的正确配置
 */
@SpringBootTest
@ActiveProfiles("test")
public class RedisConfigTest {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Test
    public void testRedisTemplateInjection() {
        // 验证RedisTemplate bean能够正确注入
        assertNotNull(redisTemplate, "RedisTemplate should be injected");
        assertNotNull(redisTemplate.getConnectionFactory(), "RedisConnectionFactory should be available");
    }

    @Test
    public void testRedisConnection() {
        try {
            // 测试基本的Redis操作
            String testKey = "test:redis:config";
            String testValue = "Redis configuration test";
            
            // 设置值
            redisTemplate.opsForValue().set(testKey, testValue);
            
            // 获取值
            Object retrievedValue = redisTemplate.opsForValue().get(testKey);
            
            assertEquals(testValue, retrievedValue, "Redis set/get operation should work");
            
            // 清理测试数据
            redisTemplate.delete(testKey);
            
        } catch (Exception e) {
            fail("Redis operations should not throw exceptions: " + e.getMessage());
        }
    }
}
