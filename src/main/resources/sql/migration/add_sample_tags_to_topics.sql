-- 为topic_bak表的现有数据添加示例tags字段
-- 执行时间：2025-06-26

-- 注意：这是一个示例脚本，实际的tags应该根据具体的题目内容和知识点结构来设置
-- 建议在生产环境中根据实际情况调整tags内容

-- 1. 为历史类题目添加示例标签
UPDATE topic_bak SET tags = '中国古代史-第1章-先秦时期-春秋战国政治制度' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 1 AND 50) 
AND tags = '' AND RAND() < 0.3;

UPDATE topic_bak SET tags = '中国古代史-第1章-先秦时期-诸子百家思想' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 1 AND 50) 
AND tags = '' AND RAND() < 0.3;

UPDATE topic_bak SET tags = '中国古代史-第2章-秦汉时期-统一制度建立' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 51 AND 100) 
AND tags = '' AND RAND() < 0.4;

UPDATE topic_bak SET tags = '中国古代史-第2章-秦汉时期-丝绸之路开辟' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 51 AND 100) 
AND tags = '' AND RAND() < 0.3;

-- 2. 为隋唐史题目添加更细分的标签（解决集中问题）
UPDATE topic_bak SET tags = '隋唐史-第3章-隋朝兴衰-大运河建设' 
WHERE know_id = 264 AND tags = '' AND title LIKE '%大运河%' OR title LIKE '%运河%';

UPDATE topic_bak SET tags = '隋唐史-第3章-隋朝兴衰-科举制创立' 
WHERE know_id = 264 AND tags = '' AND (title LIKE '%科举%' OR title LIKE '%考试%');

UPDATE topic_bak SET tags = '隋唐史-第4章-唐朝盛世-贞观之治' 
WHERE know_id = 264 AND tags = '' AND (title LIKE '%贞观%' OR title LIKE '%太宗%');

UPDATE topic_bak SET tags = '隋唐史-第4章-唐朝盛世-开元盛世' 
WHERE know_id = 264 AND tags = '' AND (title LIKE '%开元%' OR title LIKE '%玄宗%');

UPDATE topic_bak SET tags = '隋唐史-第4章-唐朝盛世-文化繁荣' 
WHERE know_id = 264 AND tags = '' AND (title LIKE '%诗歌%' OR title LIKE '%文学%' OR title LIKE '%艺术%');

UPDATE topic_bak SET tags = '隋唐史-第5章-唐朝衰落-安史之乱' 
WHERE know_id = 264 AND tags = '' AND (title LIKE '%安史%' OR title LIKE '%安禄山%');

UPDATE topic_bak SET tags = '隋唐史-第5章-唐朝衰落-藩镇割据' 
WHERE know_id = 264 AND tags = '' AND (title LIKE '%藩镇%' OR title LIKE '%割据%');

-- 3. 为其他知识点添加示例标签
UPDATE topic_bak SET tags = '宋元史-第6章-宋朝政治-王安石变法' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 200 AND 250) 
AND tags = '' AND RAND() < 0.4;

UPDATE topic_bak SET tags = '宋元史-第6章-宋朝政治-靖康之变' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 200 AND 250) 
AND tags = '' AND RAND() < 0.3;

UPDATE topic_bak SET tags = '宋元史-第7章-元朝统治-蒙古征服' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 251 AND 300) 
AND tags = '' AND RAND() < 0.4;

-- 4. 为明清史添加标签
UPDATE topic_bak SET tags = '明清史-第8章-明朝建立-朱元璋统治' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 301 AND 350) 
AND tags = '' AND RAND() < 0.3;

UPDATE topic_bak SET tags = '明清史-第8章-明朝建立-郑和下西洋' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 301 AND 350) 
AND tags = '' AND RAND() < 0.3;

UPDATE topic_bak SET tags = '明清史-第9章-清朝统治-康乾盛世' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 351 AND 400) 
AND tags = '' AND RAND() < 0.4;

-- 5. 为近现代史添加标签
UPDATE topic_bak SET tags = '中国近代史-第10章-鸦片战争-西方入侵' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 401 AND 450) 
AND tags = '' AND RAND() < 0.4;

UPDATE topic_bak SET tags = '中国近代史-第11章-洋务运动-自强求富' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 451 AND 500) 
AND tags = '' AND RAND() < 0.4;

UPDATE topic_bak SET tags = '中国现代史-第12章-新中国成立-社会主义建设' 
WHERE know_id IN (SELECT DISTINCT know_id FROM topic_bak WHERE know_id BETWEEN 501 AND 550) 
AND tags = '' AND RAND() < 0.4;

-- 6. 为一些题目添加多个标签（用逗号分隔）
UPDATE topic_bak SET tags = CONCAT(tags, ',政治制度-中央集权制度') 
WHERE tags LIKE '%统一制度%' AND tags NOT LIKE '%政治制度%';

UPDATE topic_bak SET tags = CONCAT(tags, ',经济发展-农业技术进步') 
WHERE tags LIKE '%盛世%' AND tags NOT LIKE '%经济发展%';

UPDATE topic_bak SET tags = CONCAT(tags, ',文化交流-中外文化交流') 
WHERE tags LIKE '%丝绸之路%' OR tags LIKE '%下西洋%' AND tags NOT LIKE '%文化交流%';

-- 7. 为没有标签的题目添加通用标签
UPDATE topic_bak SET tags = '历史综合-基础知识-历史概念' 
WHERE tags = '' AND type = 'choice' AND RAND() < 0.2;

UPDATE topic_bak SET tags = '历史综合-能力训练-材料分析' 
WHERE tags = '' AND type = 'short' AND RAND() < 0.3;

UPDATE topic_bak SET tags = '历史综合-基础知识-时间概念' 
WHERE tags = '' AND type = 'judge' AND RAND() < 0.2;

-- 8. 验证标签添加结果
SELECT 
    COUNT(*) as total_topics,
    COUNT(CASE WHEN tags != '' THEN 1 END) as topics_with_tags,
    ROUND(COUNT(CASE WHEN tags != '' THEN 1 END) * 100.0 / COUNT(*), 2) as tag_coverage_percentage
FROM topic_bak;

-- 9. 查看标签分布统计
SELECT 
    SUBSTRING_INDEX(tags, '-', 1) as book_name,
    COUNT(*) as topic_count
FROM topic_bak 
WHERE tags != ''
GROUP BY SUBSTRING_INDEX(tags, '-', 1)
ORDER BY topic_count DESC;

-- 注意事项：
-- 1. 这个脚本使用RAND()函数随机分配标签，实际使用时应该根据题目内容精确分配
-- 2. 标签格式遵循：书籍名称-第几章-第几章名称-第几章下面小章节名称
-- 3. 多个标签用逗号分隔
-- 4. 建议在执行前备份数据库
-- 5. 可以根据实际的知识点结构调整know_id范围和标签内容
