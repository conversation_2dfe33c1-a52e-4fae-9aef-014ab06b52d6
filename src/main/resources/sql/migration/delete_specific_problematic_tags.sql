-- 删除指定的不规范tags数据
-- 执行时间：2025-06-26
-- 目的：删除用户指定的具体问题标签

-- 执行前查看要删除的数据数量
SELECT 
    '=== 删除前统计 ===' as status,
    COUNT(*) as total_topics_to_clean
FROM topic_bak 
WHERE tags LIKE '%交通与社会变aien%'
   OR tags LIKE '%社会主义发展 ''%'
   OR tags LIKE '%社会主义发展简史-第4章-社会主义从一-国到多国发展与苏联模式%'
   OR tags LIKE '%历史选择性必修2经济与-与社会生活%'
   OR tags LIKE '%信息技术必修2信息系统与-社会%'
   OR tags LIKE '%信息技术选修三-数据管理与分析%'
   OR tags LIKE '%信息技术必-修1-%'
   OR tags LIKE '%信息技术修3数据管理与分析%';

-- ===========================================
-- 删除具体的问题标签
-- ===========================================

-- 1. 删除包含"交通与社会变aien"的标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%历史选择性必修2经济与社会生活-第5章-交通与社会变aien-现代交通运输的新变化%';

-- 2. 删除包含异常引号的社会主义发展简史标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%社会主义发展 ''社会主义发展简史-第7章-中国特色社会主义开辟社会主义新纪元''%';

-- 3. 删除包含"一-国"的标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%社会主义发展简史-第4章-社会主义从一-国到多国发展与苏联模式%';

-- 4. 删除包含"经济与-与社会生活"的标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%历史选择性必修2经济与-与社会生活-第6章-医疗与公共卫生-历史上的疫病与医学成就%';

-- 5. 删除包含"信息系统与-社会"的标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%信息技术必修2信息系统与-社会-第1章-信息技术与社会-认识信息社会%';

-- 6. 删除包含"信息技术选修三"的所有标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%信息技术选修三-数据管理与分析%';

-- 7. 删除包含"必-修1"的标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%信息技术必-修1-数据与计算-第3章-数据处理与应用-数据分析报告与应用%';

-- 8. 删除包含"信息技术修3"（缺少"选"字）的标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%信息技术修3数据管理与分析-第3章-数据的管理-数据库的操作与应用%';

-- 9. 删除包含混合格式的复杂标签（信息技术选修三 + 信息技术选修3混合）
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%信息技术选修三-数据管理与分析-第6章-初识数据仓库与数据挖掘-数据库和数据仓库,信息技术选修3数据管理与分析-第6章-初识数据仓库与数据挖掘-初识数据挖掘%';

-- 10. 删除其他包含"信息技术选修三"的单个标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%信息技术选修三-数据管理与分析-第4章-数据的分析与可视化-数据分析方法%';

UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%信息技术选修三-数据管理与分析-第4章-数据的分析与可视化-数据分析工具%';

UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%信息技术选修三-数据管理与分析-第4章-数据的分析与可视化-数据可视化%';

-- ===========================================
-- 额外清理相关的问题标签
-- ===========================================

-- 11. 清理所有包含"选修三"的标签（应该是"选修3"）
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%选修三%';

-- 12. 清理所有包含"-与-"的异常格式
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%-与-%';

-- 13. 清理所有包含"必-修"的标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%必-修%';

-- 14. 清理包含异常字符"aien"的所有标签
UPDATE topic_bak SET tags = '' 
WHERE tags LIKE '%aien%';

-- ===========================================
-- 验证删除结果
-- ===========================================

-- 15. 检查是否还有相关问题标签
SELECT 
    '=== 删除后问题检查 ===' as status,
    COUNT(CASE WHEN tags LIKE '%交通与社会变aien%' THEN 1 END) as aien_issues,
    COUNT(CASE WHEN tags LIKE '%社会主义发展 ''%' THEN 1 END) as quote_issues,
    COUNT(CASE WHEN tags LIKE '%一-国%' THEN 1 END) as dash_issues,
    COUNT(CASE WHEN tags LIKE '%-与-%' THEN 1 END) as double_dash_issues,
    COUNT(CASE WHEN tags LIKE '%选修三%' THEN 1 END) as xuanxiu_issues,
    COUNT(CASE WHEN tags LIKE '%必-修%' THEN 1 END) as bixiu_issues
FROM topic_bak;

-- 16. 统计删除后的整体情况
SELECT 
    '=== 删除后总体统计 ===' as status,
    COUNT(*) as total_topics,
    COUNT(CASE WHEN tags != '' THEN 1 END) as topics_with_tags,
    ROUND(COUNT(CASE WHEN tags != '' THEN 1 END) * 100.0 / COUNT(*), 2) as tag_coverage_percentage
FROM topic_bak;

-- 17. 显示删除后剩余的书名分布
SELECT 
    '=== 剩余书名分布 ===' as status,
    SUBSTRING_INDEX(tags, '-', 1) as book_name,
    COUNT(*) as topic_count
FROM topic_bak 
WHERE tags != ''
GROUP BY SUBSTRING_INDEX(tags, '-', 1)
ORDER BY topic_count DESC
LIMIT 15;

-- 18. 显示一些保留的正常标签示例
SELECT 
    '=== 保留的正常标签示例 ===' as status,
    tags
FROM topic_bak 
WHERE tags != '' 
  AND tags NOT LIKE '%aien%'
  AND tags NOT LIKE '%选修三%'
  AND tags NOT LIKE '%-与-%'
  AND tags NOT LIKE '%必-修%'
ORDER BY RAND() 
LIMIT 5;

-- 执行说明：
-- 1. 这个脚本专门删除用户指定的问题标签
-- 2. 删除后这些题目的tags字段将为空字符串
-- 3. 可以后续为这些题目重新分配正确的标签
-- 4. 建议执行前备份数据
