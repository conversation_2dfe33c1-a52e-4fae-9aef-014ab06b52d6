-- 删除不规范的tags数据
-- 执行时间：2025-06-26
-- 目的：清理数据质量问题，删除有问题的标签数据

-- 执行前备份（可选）
-- CREATE TABLE topic_bak_tags_backup AS SELECT id, tags FROM topic_bak WHERE tags != '';

-- ===========================================
-- 删除包含异常字符的tags
-- ===========================================

-- 1. 删除包含编码问题字符的tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%ת%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%与与%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%lerei''s%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%aien%';

-- 2. 删除包含多余连字符的tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%---%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%必-修%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%信息-技术%';

-- 3. 删除以单引号开头的异常tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '''%';

-- ===========================================
-- 删除截断的书名tags
-- ===========================================

-- 4. 删除明显截断的书名
UPDATE topic_bak SET tags = '' WHERE tags = '信息技术必';
UPDATE topic_bak SET tags = '' WHERE tags = '习近平新';
UPDATE topic_bak SET tags = '' WHERE tags = '信息技术必修1数据与ת';
UPDATE topic_bak SET tags = '' WHERE tags = '信息技术修3数据管理与分析';
UPDATE topic_bak SET tags = '' WHERE tags = '信息技术选修三';
UPDATE topic_bak SET tags = '' WHERE tags = '历史选择性必修2';
UPDATE topic_bak SET tags = '' WHERE tags = '历史选择性必修2经济与';
UPDATE topic_bak SET tags = '' WHERE tags = '社会主义发展';

-- 5. 删除包含截断书名的复合tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '信息技术必-%' AND tags NOT LIKE '信息技术必修%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '习近平新-%' AND tags NOT LIKE '习近平新时代中国特色社会主义思想学生读本%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '历史选择性必修2经济与-%' AND tags NOT LIKE '%社会生活%';

-- ===========================================
-- 删除格式严重不规范的tags
-- ===========================================

-- 6. 删除过短的tags（可能不完整）
UPDATE topic_bak SET tags = '' WHERE LENGTH(tags) < 10 AND tags != '';

-- 7. 删除包含异常空格和格式的tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%信息技术必修2信息系统与-%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%信息技术必修2信息系统与社会%' AND tags LIKE '%与-%';

-- 8. 删除包含特殊字符组合的tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%曲-折%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%一-国%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%新-时代%';

-- ===========================================
-- 删除重复字符问题的tags
-- ===========================================

-- 9. 删除包含重复字符的tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%信息技术选修3数据管理与与分析%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%信息技术必修1数据与与计算%';

-- ===========================================
-- 删除包含异常标点的tags
-- ===========================================

-- 10. 删除包含异常引号的tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%"四个全面''%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%"四个全面''%';

-- ===========================================
-- 删除明显错误的书名变体
-- ===========================================

-- 11. 删除各种错误的书名变体
UPDATE topic_bak SET tags = '' WHERE SUBSTRING_INDEX(tags, '-', 1) IN (
    '信息技术必',
    '习近平新',
    '信息技术选修三',
    '历史选择性必修2',
    '信息技术修3数据管理与分析',
    '信息技术必修1数据与ת',
    '社会主义发展',
    '信息技术必修2信息系统与',
    '历史选择性必修2经济与'
);

-- 12. 删除包含明显拼写错误的tags
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%近代以lerei''s city化进程%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%交通与社会变aien%';
UPDATE topic_bak SET tags = '' WHERE tags LIKE '%历史选择性必修2经济与-与社会生活%';

-- ===========================================
-- 验证删除结果
-- ===========================================

-- 13. 统计删除后的结果
SELECT 
    '=== 删除后统计 ===' as status,
    COUNT(*) as total_topics,
    COUNT(CASE WHEN tags != '' THEN 1 END) as topics_with_tags,
    ROUND(COUNT(CASE WHEN tags != '' THEN 1 END) * 100.0 / COUNT(*), 2) as tag_coverage_percentage
FROM topic_bak;

-- 14. 检查是否还有问题数据
SELECT 
    '=== 剩余问题检查 ===' as status,
    COUNT(CASE WHEN tags LIKE '%ת%' THEN 1 END) as encoding_issues,
    COUNT(CASE WHEN tags LIKE '%与与%' THEN 1 END) as duplicate_chars,
    COUNT(CASE WHEN tags LIKE '%---%' THEN 1 END) as multiple_dashes,
    COUNT(CASE WHEN LENGTH(SUBSTRING_INDEX(tags, '-', 1)) < 5 AND tags != '' THEN 1 END) as short_book_names,
    COUNT(CASE WHEN tags LIKE '%''%' THEN 1 END) as quote_issues
FROM topic_bak;

-- 15. 显示清理后的书名分布（应该都是规范的）
SELECT 
    SUBSTRING_INDEX(tags, '-', 1) as book_name,
    COUNT(*) as topic_count
FROM topic_bak 
WHERE tags != ''
GROUP BY SUBSTRING_INDEX(tags, '-', 1)
ORDER BY topic_count DESC
LIMIT 20;

-- 16. 显示一些保留的标签示例
SELECT 
    '=== 保留的标签示例 ===' as status,
    tags
FROM topic_bak 
WHERE tags != '' 
ORDER BY RAND() 
LIMIT 10;

-- 执行说明：
-- 1. 这个脚本会删除所有有问题的tags，将其设置为空字符串
-- 2. 建议先在测试环境执行
-- 3. 执行前可以备份tags字段
-- 4. 执行后只保留格式规范的标签数据
-- 5. 后续可以重新为空标签的题目分配正确的标签
