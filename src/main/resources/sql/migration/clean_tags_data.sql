-- 清理和标准化topic_bak表中的tags字段数据
-- 执行时间：2025-06-26
-- 目的：修复数据录入不规范、字符编码问题、截断问题等

-- 执行前备份
-- CREATE TABLE topic_bak_backup AS SELECT * FROM topic_bak WHERE tags != '';

-- ===========================================
-- 第一阶段：基础数据清理
-- ===========================================

-- 1. 清理异常字符和编码问题
UPDATE topic_bak SET tags = REPLACE(tags, 'ת', '计算') WHERE tags LIKE '%ת%';
UPDATE topic_bak SET tags = REPLACE(tags, '与与', '与') WHERE tags LIKE '%与与%';
UPDATE topic_bak SET tags = REPLACE(tags, 'lerei''s', '来的') WHERE tags LIKE '%lerei''s%';
UPDATE topic_bak SET tags = REPLACE(tags, 'aien', '迁') WHERE tags LIKE '%aien%';
UPDATE topic_bak SET tags = TRIM(REPLACE(tags, '''', '')) WHERE tags LIKE '''%';

-- 2. 清理多余的连字符和空格
UPDATE topic_bak SET tags = REPLACE(tags, '--', '-') WHERE tags LIKE '%--%';
UPDATE topic_bak SET tags = REPLACE(tags, '---', '-') WHERE tags LIKE '%---%';
UPDATE topic_bak SET tags = REPLACE(tags, '必-修', '必修') WHERE tags LIKE '%必-修%';
UPDATE topic_bak SET tags = REPLACE(tags, '信息-技术', '信息技术') WHERE tags LIKE '%信息-技术%';
UPDATE topic_bak SET tags = REPLACE(tags, '新-时代', '新时代') WHERE tags LIKE '%新-时代%';
UPDATE topic_bak SET tags = REPLACE(tags, '曲-折', '曲折') WHERE tags LIKE '%曲-折%';
UPDATE topic_bak SET tags = REPLACE(tags, '一-国', '一国') WHERE tags LIKE '%一-国%';

-- 3. 清理空格问题
UPDATE topic_bak SET tags = REPLACE(tags, '  ', ' ') WHERE tags LIKE '%  %';
UPDATE topic_bak SET tags = REPLACE(tags, ' -', '-') WHERE tags LIKE '% -%';
UPDATE topic_bak SET tags = REPLACE(tags, '- ', '-') WHERE tags LIKE '%- %';
UPDATE topic_bak SET tags = TRIM(tags) WHERE tags LIKE ' %' OR tags LIKE '% ';

-- ===========================================
-- 第二阶段：修复截断的书名
-- ===========================================

-- 4. 修复信息技术相关书名
UPDATE topic_bak SET tags = CONCAT('信息技术必修1数据与计算', SUBSTRING(tags, LENGTH('信息技术必') + 1))
WHERE tags LIKE '信息技术必-%' AND tags NOT LIKE '信息技术必修%';

UPDATE topic_bak SET tags = '信息技术必修1数据与计算'
WHERE tags = '信息技术必';

UPDATE topic_bak SET tags = REPLACE(tags, '信息技术修3数据管理与分析', '信息技术选修3数据管理与分析')
WHERE tags LIKE '%信息技术修3数据管理与分析%';

UPDATE topic_bak SET tags = REPLACE(tags, '信息技术选修三', '信息技术选修3数据管理与分析')
WHERE tags LIKE '信息技术选修三%';

-- 5. 修复习近平相关书名
UPDATE topic_bak SET tags = CONCAT('习近平新时代中国特色社会主义思想学生读本', SUBSTRING(tags, LENGTH('习近平新') + 1))
WHERE tags LIKE '习近平新-%' AND tags NOT LIKE '习近平新时代中国特色社会主义思想学生读本%';

UPDATE topic_bak SET tags = '习近平新时代中国特色社会主义思想学生读本'
WHERE tags = '习近平新';

-- 6. 修复历史相关书名
UPDATE topic_bak SET tags = CONCAT('历史选择性必修2经济与社会生活', SUBSTRING(tags, LENGTH('历史选择性必修2经济与') + 1))
WHERE tags LIKE '历史选择性必修2经济与-%' AND tags NOT LIKE '%社会生活%';

UPDATE topic_bak SET tags = CONCAT('历史选择性必修2经济与社会生活', SUBSTRING(tags, LENGTH('历史选择性必修2') + 1))
WHERE tags LIKE '历史选择性必修2-%' AND tags NOT LIKE '%经济与社会生活%';

-- 7. 修复社会主义发展简史
UPDATE topic_bak SET tags = CONCAT('社会主义发展简史', SUBSTRING(tags, LENGTH('社会主义发展') + 1))
WHERE tags LIKE '社会主义发展 %' OR tags LIKE '社会主义发展''%';

-- ===========================================
-- 第三阶段：标准化重复字符
-- ===========================================

-- 8. 修复重复字符问题
UPDATE topic_bak SET tags = REPLACE(tags, '信息技术选修3数据管理与与分析', '信息技术选修3数据管理与分析')
WHERE tags LIKE '%信息技术选修3数据管理与与分析%';

UPDATE topic_bak SET tags = REPLACE(tags, '信息技术必修1数据与与计算', '信息技术必修1数据与计算')
WHERE tags LIKE '%信息技术必修1数据与与计算%';

UPDATE topic_bak SET tags = REPLACE(tags, '信息技术必修2信息系统与-社会', '信息技术必修2信息系统与社会')
WHERE tags LIKE '%信息技术必修2信息系统与-社会%';

UPDATE topic_bak SET tags = REPLACE(tags, '信息技术必修2信息系统与社会', '信息技术必修2信息系统与社会')
WHERE tags LIKE '%信息技术必修2信息系统与%' AND tags NOT LIKE '%信息技术必修2信息系统与社会%';

-- ===========================================
-- 第四阶段：修复标点符号问题
-- ===========================================

-- 9. 修复引号和标点问题
UPDATE topic_bak SET tags = REPLACE(tags, '"四个全面''', '"四个全面"')
WHERE tags LIKE '%"四个全面''%';

UPDATE topic_bak SET tags = REPLACE(tags, '"四个全面'', '"四个全面"')
WHERE tags LIKE '%"四个全面''%';

-- ===========================================
-- 第五阶段：验证和统计
-- ===========================================

-- 10. 最终清理：移除首尾空格和多余字符
UPDATE topic_bak SET tags = TRIM(tags) WHERE tags != '';

-- 11. 验证清理结果
SELECT 
    '=== 数据清理完成统计 ===' as status,
    COUNT(*) as total_topics,
    COUNT(CASE WHEN tags != '' THEN 1 END) as topics_with_tags,
    ROUND(COUNT(CASE WHEN tags != '' THEN 1 END) * 100.0 / COUNT(*), 2) as tag_coverage_percentage
FROM topic_bak;

-- 12. 检查是否还有问题数据
SELECT 
    '=== 问题数据检查 ===' as status,
    COUNT(CASE WHEN tags LIKE '%ת%' THEN 1 END) as encoding_issues,
    COUNT(CASE WHEN tags LIKE '%与与%' THEN 1 END) as duplicate_chars,
    COUNT(CASE WHEN tags LIKE '%---%' THEN 1 END) as multiple_dashes,
    COUNT(CASE WHEN LENGTH(SUBSTRING_INDEX(tags, '-', 1)) < 5 THEN 1 END) as short_book_names,
    COUNT(CASE WHEN tags LIKE '% %' THEN 1 END) as space_issues
FROM topic_bak 
WHERE tags != '';

-- 13. 显示清理后的书名分布（前20个）
SELECT 
    SUBSTRING_INDEX(tags, '-', 1) as book_name,
    COUNT(*) as topic_count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM topic_bak WHERE tags != ''), 2) as percentage
FROM topic_bak 
WHERE tags != ''
GROUP BY SUBSTRING_INDEX(tags, '-', 1)
ORDER BY topic_count DESC
LIMIT 20;

-- 14. 检查标签格式是否符合规范
SELECT 
    '=== 标签格式检查 ===' as status,
    COUNT(CASE WHEN tags REGEXP '^[^-]+-第[0-9]+章-[^-]+-[^-]+$' THEN 1 END) as standard_4_part_tags,
    COUNT(CASE WHEN tags REGEXP '^[^-]+-第[0-9]+章-[^-]+$' THEN 1 END) as standard_3_part_tags,
    COUNT(CASE WHEN tags LIKE '%,%' THEN 1 END) as multi_tags,
    COUNT(CASE WHEN tags != '' AND tags NOT REGEXP '^[^-]+-第[0-9]+章' THEN 1 END) as non_standard_format
FROM topic_bak 
WHERE tags != '';

-- 15. 显示一些示例清理后的标签
SELECT 
    '=== 清理后标签示例 ===' as status,
    tags,
    LENGTH(tags) as tag_length
FROM topic_bak 
WHERE tags != '' 
ORDER BY RAND() 
LIMIT 10;

-- 执行说明：
-- 1. 建议先在测试环境执行
-- 2. 执行前请备份topic_bak表
-- 3. 可以分段执行，每个阶段后检查结果
-- 4. 如果发现新的问题模式，可以添加相应的清理规则
