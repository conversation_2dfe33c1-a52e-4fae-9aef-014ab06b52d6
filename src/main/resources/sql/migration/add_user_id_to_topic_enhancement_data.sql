-- 为topic_enhancement_data表添加用户ID字段的迁移脚本
-- 执行时间：2025-06-26

-- 1. 添加user_id字段
ALTER TABLE `topic_enhancement_data` 
ADD COLUMN `user_id` BIGINT NOT NULL DEFAULT 0 COMMENT '用户ID - 用于区分不同用户的使用数据' AFTER `topic_id`;

-- 2. 删除原有的唯一索引（只基于topic_id）
DROP INDEX `uk_topic_id` ON `topic_enhancement_data`;

-- 3. 创建新的复合唯一索引（基于topic_id和user_id）
CREATE UNIQUE INDEX `uk_topic_user` ON `topic_enhancement_data` (`topic_id`, `user_id`);

-- 4. 为user_id字段添加普通索引以提高查询性能
CREATE INDEX `idx_user_id` ON `topic_enhancement_data` (`user_id`);

-- 5. 为last_used_time字段添加索引以支持时间范围查询
CREATE INDEX `idx_last_used_time` ON `topic_enhancement_data` (`last_used_time`);

-- 注意：执行此脚本前请备份数据库
-- 如果表中已有数据，需要先为现有数据设置合适的user_id值
