package com.edu.maizi_edu_sys.dto;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class PaperGenerationRequest {

    /**
     * Configuration for each knowledge point.
     */
    @NotEmpty(message = "Knowledge point configurations cannot be empty")
    @Valid // Ensures validation of elements within the list
    private List<KnowledgePointConfigRequest> knowledgePointConfigs;

    /**
     * 试卷标题
     */
    @NotBlank(message = "试卷标题不能为空") // Changed from @NotEmpty to @NotBlank for better string validation
    private String title;

    /**
     * 总分 - This might be dynamically calculated based on knowledgePointConfigs and typeScoreMap
     * Or it can be a target score the engine tries to achieve.
     * For now, let's keep it, but its role might change.
     */
    @NotNull(message = "总分不能为空")
    @Min(value = 0, message = "总分不能为负") // Min score can be 0 if no questions are generated
    private Integer totalScore; // Consider if this should be removed if dynamically calculated

    /**
     * 题型-分值映射 (Global score per question type)
     */
    @NotNull(message = "题型-分值映射不能为空") // Can be empty if not used, but not null
    private Map<String, Integer> typeScoreMap;

    /**
     * 难度标准（百分比）(Global difficulty distribution)
     */
    @NotNull(message = "难度标准不能为空")
    @Valid // If DifficultyCriteria is a complex object, otherwise not needed for Map<String, Double>
    private Map<String, Double> difficultyCriteria;

    /**
     * 题型数量配置 (Global count per question type)
     * This might be used as a global constraint or overridden by per-knowledge point counts.
     */
    @NotNull(message = "题型配置不能为空") // Can be empty if not used, but not null
    private Map<String, Integer> topicTypeCounts;
    
    /**
     * 最小重用间隔（天）- 可选
     */
    private Integer minReuseIntervalDays;
    
    // The getCognitiveLevelCriteria method can be kept if still needed for other parts of the system
    // or removed if it's no longer relevant to paper generation with these changes.
    // For now, I'll comment it out to simplify.
    // Updated: Uncommenting as it is used by the PaperGenerationEngine
    public Map<String, Double> getCognitiveLevelCriteria() {
        // Provide a default implementation or ensure it's set appropriately before use.
        // For now, returning null or an empty map might be safer if not fully implemented.
        // Let's return a default as it was previously structured.
        Map<String, Double> defaultMap = new java.util.HashMap<>(); // Ensure HashMap is imported or use fully qualified name
        defaultMap.put("remember", 0.3);
        defaultMap.put("understand", 0.3);
        defaultMap.put("apply", 0.4);
        return defaultMap;
    }

    /**
     * 获取知识点ID列表
     * 从knowledgePointConfigs中提取所有知识点ID
     * @return 知识点ID列表
     */
    public List<Long> getKnowledgeIds() {
        if (knowledgePointConfigs == null || knowledgePointConfigs.isEmpty()) {
            return new java.util.ArrayList<>();
        }

        return knowledgePointConfigs.stream()
            .map(KnowledgePointConfigRequest::getKnowledgeId)
            .filter(java.util.Objects::nonNull)
            .collect(java.util.stream.Collectors.toList());
    }
}