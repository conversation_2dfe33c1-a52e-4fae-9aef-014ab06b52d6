package com.edu.maizi_edu_sys.entity;

import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 题目增强数据实体类
 * 用于存储题目的认知层次等信息
 */
@Data
@Entity
@Table(name = "topic_enhancement_data")
public class TopicEnhancementData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 题目ID
     */
    @Column(name = "topic_id", nullable = false)
    private Integer topicId;

    /**
     * 用户ID - 用于区分不同用户的使用数据
     */
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 认知层次
     * 可选值：理解、应用、分析、评价
     * @deprecated 此字段将在未来版本移除，保留是为了向后兼容
     */
    @Deprecated
    @Column(name = "cognitive_level")
    private String cognitiveLevel;

    /**
     * 使用次数
     */
    @Column(name = "usage_count")
    private Integer usageCount = 0;
    
    /**
     * 最后使用时间
     */
    @Column(name = "last_used_time")
    private LocalDateTime lastUsedTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime = LocalDateTime.now();

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime = LocalDateTime.now();
} 