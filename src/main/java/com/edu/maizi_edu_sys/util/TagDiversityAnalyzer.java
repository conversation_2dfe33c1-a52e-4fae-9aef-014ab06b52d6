package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.entity.Topic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签多样性分析器
 * 用于分析和优化题目选择的标签分布，避免过度集中在某个章节
 */
@Component
@Slf4j
public class TagDiversityAnalyzer {
    
    /**
     * 解析题目的标签列表
     * @param topic 题目
     * @return 标签列表
     */
    public List<String> parseTopicTags(Topic topic) {
        if (topic == null || topic.getTags() == null || topic.getTags().trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        return Arrays.stream(topic.getTags().split(","))
                .map(String::trim)
                .filter(tag -> !tag.isEmpty())
                .collect(Collectors.toList());
    }
    
    /**
     * 计算题目列表的标签多样性分数
     * @param topics 题目列表
     * @return 多样性分数，范围[0,1]，1表示最大多样性
     */
    public double calculateTagDiversity(List<Topic> topics) {
        if (topics == null || topics.isEmpty()) {
            return 0.0;
        }
        
        // 统计所有标签的出现频次
        Map<String, Integer> tagCounts = new HashMap<>();
        int totalTagOccurrences = 0;
        
        for (Topic topic : topics) {
            List<String> tags = parseTopicTags(topic);
            for (String tag : tags) {
                tagCounts.put(tag, tagCounts.getOrDefault(tag, 0) + 1);
                totalTagOccurrences++;
            }
        }
        
        if (totalTagOccurrences == 0) {
            return 0.0;
        }
        
        // 计算香农熵作为多样性指标
        double entropy = 0.0;
        for (int count : tagCounts.values()) {
            double probability = (double) count / totalTagOccurrences;
            if (probability > 0) {
                entropy -= probability * Math.log(probability) / Math.log(2);
            }
        }
        
        // 归一化到[0,1]范围
        // 最大熵 = log2(不同标签数量)
        double maxEntropy = tagCounts.size() > 1 ? Math.log(tagCounts.size()) / Math.log(2) : 1.0;
        return maxEntropy > 0 ? entropy / maxEntropy : 0.0;
    }
    
    /**
     * 分析标签分布，返回详细统计信息
     * @param topics 题目列表
     * @return 标签分布统计
     */
    public TagDistributionStats analyzeTagDistribution(List<Topic> topics) {
        Map<String, Integer> tagCounts = new HashMap<>();
        Map<String, Set<Integer>> tagToTopicIds = new HashMap<>();
        int topicsWithTags = 0;
        int totalTopics = topics.size();
        
        for (Topic topic : topics) {
            List<String> tags = parseTopicTags(topic);
            if (!tags.isEmpty()) {
                topicsWithTags++;
                for (String tag : tags) {
                    tagCounts.put(tag, tagCounts.getOrDefault(tag, 0) + 1);
                    tagToTopicIds.computeIfAbsent(tag, k -> new HashSet<>()).add(topic.getId());
                }
            }
        }
        
        double diversity = calculateTagDiversity(topics);
        double tagCoverage = totalTopics > 0 ? (double) topicsWithTags / totalTopics : 0.0;
        
        return new TagDistributionStats(
            tagCounts,
            tagToTopicIds,
            diversity,
            tagCoverage,
            totalTopics,
            topicsWithTags
        );
    }
    
    /**
     * 检查标签分布是否过于集中
     * @param topics 题目列表
     * @param concentrationThreshold 集中度阈值，超过此比例认为过于集中
     * @return 是否过于集中
     */
    public boolean isTagDistributionTooConcentrated(List<Topic> topics, double concentrationThreshold) {
        TagDistributionStats stats = analyzeTagDistribution(topics);
        
        if (stats.getTagCounts().isEmpty()) {
            return false; // 没有标签信息，无法判断
        }
        
        // 找出最频繁的标签
        int maxCount = stats.getTagCounts().values().stream().mapToInt(Integer::intValue).max().orElse(0);
        int totalTagOccurrences = stats.getTagCounts().values().stream().mapToInt(Integer::intValue).sum();
        
        double maxTagRatio = totalTagOccurrences > 0 ? (double) maxCount / totalTagOccurrences : 0.0;
        
        return maxTagRatio > concentrationThreshold;
    }
    
    /**
     * 获取标签的层级信息
     * @param tag 标签字符串
     * @return 标签层级信息
     */
    public TagHierarchy parseTagHierarchy(String tag) {
        if (tag == null || tag.trim().isEmpty()) {
            return new TagHierarchy("", "", "", "");
        }
        
        String[] parts = tag.split("-");
        String book = parts.length > 0 ? parts[0].trim() : "";
        String chapter = parts.length > 1 ? parts[1].trim() : "";
        String chapterName = parts.length > 2 ? parts[2].trim() : "";
        String section = parts.length > 3 ? parts[3].trim() : "";
        
        return new TagHierarchy(book, chapter, chapterName, section);
    }
    
    /**
     * 计算两个标签的相似度
     * @param tag1 标签1
     * @param tag2 标签2
     * @return 相似度分数，范围[0,1]
     */
    public double calculateTagSimilarity(String tag1, String tag2) {
        if (tag1 == null || tag2 == null) {
            return 0.0;
        }
        
        TagHierarchy h1 = parseTagHierarchy(tag1);
        TagHierarchy h2 = parseTagHierarchy(tag2);
        
        double similarity = 0.0;
        
        // 书籍相同 +0.4
        if (h1.getBook().equals(h2.getBook()) && !h1.getBook().isEmpty()) {
            similarity += 0.4;
            
            // 章节相同 +0.3
            if (h1.getChapter().equals(h2.getChapter()) && !h1.getChapter().isEmpty()) {
                similarity += 0.3;
                
                // 章节名称相同 +0.2
                if (h1.getChapterName().equals(h2.getChapterName()) && !h1.getChapterName().isEmpty()) {
                    similarity += 0.2;
                    
                    // 小节相同 +0.1
                    if (h1.getSection().equals(h2.getSection()) && !h1.getSection().isEmpty()) {
                        similarity += 0.1;
                    }
                }
            }
        }
        
        return similarity;
    }
    
    /**
     * 标签分布统计信息
     */
    public static class TagDistributionStats {
        private final Map<String, Integer> tagCounts;
        private final Map<String, Set<Integer>> tagToTopicIds;
        private final double diversity;
        private final double tagCoverage;
        private final int totalTopics;
        private final int topicsWithTags;
        
        public TagDistributionStats(Map<String, Integer> tagCounts, 
                                  Map<String, Set<Integer>> tagToTopicIds,
                                  double diversity, double tagCoverage, 
                                  int totalTopics, int topicsWithTags) {
            this.tagCounts = tagCounts;
            this.tagToTopicIds = tagToTopicIds;
            this.diversity = diversity;
            this.tagCoverage = tagCoverage;
            this.totalTopics = totalTopics;
            this.topicsWithTags = topicsWithTags;
        }
        
        // Getters
        public Map<String, Integer> getTagCounts() { return tagCounts; }
        public Map<String, Set<Integer>> getTagToTopicIds() { return tagToTopicIds; }
        public double getDiversity() { return diversity; }
        public double getTagCoverage() { return tagCoverage; }
        public int getTotalTopics() { return totalTopics; }
        public int getTopicsWithTags() { return topicsWithTags; }
    }
    
    /**
     * 标签层级信息
     */
    public static class TagHierarchy {
        private final String book;
        private final String chapter;
        private final String chapterName;
        private final String section;
        
        public TagHierarchy(String book, String chapter, String chapterName, String section) {
            this.book = book;
            this.chapter = chapter;
            this.chapterName = chapterName;
            this.section = section;
        }
        
        // Getters
        public String getBook() { return book; }
        public String getChapter() { return chapter; }
        public String getChapterName() { return chapterName; }
        public String getSection() { return section; }
        
        @Override
        public String toString() {
            return String.format("TagHierarchy{book='%s', chapter='%s', chapterName='%s', section='%s'}", 
                               book, chapter, chapterName, section);
        }
    }
}
