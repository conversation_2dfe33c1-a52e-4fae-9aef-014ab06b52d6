package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.entity.Topic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 标签数据验证和修复工具
 * 用于检测和修复tags字段中的数据质量问题
 */
@Component
@Slf4j
public class TagDataValidator {
    
    // 标准标签格式的正则表达式
    private static final Pattern STANDARD_TAG_PATTERN = Pattern.compile("^[^-]+-第\\d+章-[^-]+-[^-]+$");
    private static final Pattern CHAPTER_PATTERN = Pattern.compile("^[^-]+-第\\d+章-[^-]+$");
    
    // 常见的问题字符
    private static final Map<String, String> PROBLEM_CHARS = new HashMap<>();
    static {
        PROBLEM_CHARS.put("ת", "计算");
        PROBLEM_CHARS.put("与与", "与");
        PROBLEM_CHARS.put("--", "-");
        PROBLEM_CHARS.put("必-修", "必修");
        PROBLEM_CHARS.put("信息-技术", "信息技术");
        PROBLEM_CHARS.put("新-时代", "新时代");
        PROBLEM_CHARS.put("曲-折", "曲折");
        PROBLEM_CHARS.put("一-国", "一国");
        PROBLEM_CHARS.put("lerei's", "来的");
        PROBLEM_CHARS.put("aien", "迁");
    }
    
    // 标准书名映射
    private static final Map<String, String> BOOK_NAME_FIXES = new HashMap<>();
    static {
        BOOK_NAME_FIXES.put("信息技术必", "信息技术必修1数据与计算");
        BOOK_NAME_FIXES.put("习近平新", "习近平新时代中国特色社会主义思想学生读本");
        BOOK_NAME_FIXES.put("信息技术选修三", "信息技术选修3数据管理与分析");
        BOOK_NAME_FIXES.put("历史选择性必修2经济与", "历史选择性必修2经济与社会生活");
        BOOK_NAME_FIXES.put("社会主义发展", "社会主义发展简史");
    }
    
    /**
     * 验证单个题目的标签质量
     */
    public TagValidationResult validateTopicTags(Topic topic) {
        if (topic == null || topic.getTags() == null) {
            return new TagValidationResult(false, "题目或标签为空", Collections.emptyList());
        }
        
        String tags = topic.getTags().trim();
        if (tags.isEmpty()) {
            return new TagValidationResult(true, "标签为空，无需验证", Collections.emptyList());
        }
        
        List<String> issues = new ArrayList<>();
        List<String> tagList = Arrays.stream(tags.split(","))
                .map(String::trim)
                .filter(tag -> !tag.isEmpty())
                .collect(Collectors.toList());
        
        for (String tag : tagList) {
            validateSingleTag(tag, issues);
        }
        
        boolean isValid = issues.isEmpty();
        String message = isValid ? "标签格式正确" : "发现 " + issues.size() + " 个问题";
        
        return new TagValidationResult(isValid, message, issues);
    }
    
    /**
     * 验证单个标签
     */
    private void validateSingleTag(String tag, List<String> issues) {
        // 检查异常字符
        for (Map.Entry<String, String> entry : PROBLEM_CHARS.entrySet()) {
            if (tag.contains(entry.getKey())) {
                issues.add("包含异常字符: " + entry.getKey() + " -> 应为: " + entry.getValue());
            }
        }
        
        // 检查书名截断
        String bookName = getBookNameFromTag(tag);
        for (String truncated : BOOK_NAME_FIXES.keySet()) {
            if (bookName.equals(truncated)) {
                issues.add("书名截断: " + truncated + " -> 应为: " + BOOK_NAME_FIXES.get(truncated));
            }
        }
        
        // 检查格式规范性
        if (!STANDARD_TAG_PATTERN.matcher(tag).matches() && !CHAPTER_PATTERN.matcher(tag).matches()) {
            issues.add("标签格式不规范: " + tag);
        }
        
        // 检查长度
        if (tag.length() < 10) {
            issues.add("标签过短，可能不完整: " + tag);
        }
        
        if (tag.length() > 200) {
            issues.add("标签过长: " + tag);
        }
    }
    
    /**
     * 修复单个题目的标签
     */
    public String fixTopicTags(Topic topic) {
        if (topic == null || topic.getTags() == null) {
            return "";
        }
        
        String tags = topic.getTags().trim();
        if (tags.isEmpty()) {
            return "";
        }
        
        // 分割多个标签
        List<String> tagList = Arrays.stream(tags.split(","))
                .map(String::trim)
                .filter(tag -> !tag.isEmpty())
                .map(this::fixSingleTag)
                .filter(tag -> !tag.isEmpty())
                .collect(Collectors.toList());
        
        return String.join(",", tagList);
    }
    
    /**
     * 修复单个标签
     */
    private String fixSingleTag(String tag) {
        String fixed = tag;
        
        // 修复异常字符
        for (Map.Entry<String, String> entry : PROBLEM_CHARS.entrySet()) {
            fixed = fixed.replace(entry.getKey(), entry.getValue());
        }
        
        // 修复书名截断
        String bookName = getBookNameFromTag(fixed);
        for (Map.Entry<String, String> entry : BOOK_NAME_FIXES.entrySet()) {
            if (bookName.equals(entry.getKey())) {
                fixed = fixed.replace(entry.getKey(), entry.getValue());
                break;
            }
        }
        
        // 清理空格和多余字符
        fixed = fixed.replaceAll("\\s+", " ").trim();
        fixed = fixed.replace(" -", "-").replace("- ", "-");
        
        return fixed;
    }
    
    /**
     * 从标签中提取书名
     */
    private String getBookNameFromTag(String tag) {
        int firstDash = tag.indexOf('-');
        return firstDash > 0 ? tag.substring(0, firstDash) : tag;
    }
    
    /**
     * 批量验证题目列表的标签质量
     */
    public TagBatchValidationResult validateTopicListTags(List<Topic> topics) {
        if (topics == null || topics.isEmpty()) {
            return new TagBatchValidationResult(0, 0, 0, Collections.emptyMap());
        }
        
        int totalTopics = topics.size();
        int validTopics = 0;
        int topicsWithTags = 0;
        Map<String, Integer> issueStats = new HashMap<>();
        
        for (Topic topic : topics) {
            if (topic.getTags() != null && !topic.getTags().trim().isEmpty()) {
                topicsWithTags++;
                
                TagValidationResult result = validateTopicTags(topic);
                if (result.isValid()) {
                    validTopics++;
                } else {
                    // 统计问题类型
                    for (String issue : result.getIssues()) {
                        String issueType = extractIssueType(issue);
                        issueStats.put(issueType, issueStats.getOrDefault(issueType, 0) + 1);
                    }
                }
            }
        }
        
        return new TagBatchValidationResult(totalTopics, topicsWithTags, validTopics, issueStats);
    }
    
    /**
     * 提取问题类型
     */
    private String extractIssueType(String issue) {
        if (issue.contains("异常字符")) return "异常字符";
        if (issue.contains("书名截断")) return "书名截断";
        if (issue.contains("格式不规范")) return "格式不规范";
        if (issue.contains("过短")) return "标签过短";
        if (issue.contains("过长")) return "标签过长";
        return "其他问题";
    }
    
    /**
     * 生成数据质量报告
     */
    public String generateQualityReport(TagBatchValidationResult result) {
        StringBuilder report = new StringBuilder();
        report.append("=== 标签数据质量报告 ===\n");
        report.append("总题目数: ").append(result.getTotalTopics()).append("\n");
        report.append("有标签题目数: ").append(result.getTopicsWithTags()).append("\n");
        report.append("标签覆盖率: ").append(String.format("%.2f%%", 
                result.getTotalTopics() > 0 ? (double) result.getTopicsWithTags() / result.getTotalTopics() * 100 : 0)).append("\n");
        report.append("标签有效题目数: ").append(result.getValidTopics()).append("\n");
        report.append("标签有效率: ").append(String.format("%.2f%%", 
                result.getTopicsWithTags() > 0 ? (double) result.getValidTopics() / result.getTopicsWithTags() * 100 : 0)).append("\n");
        
        if (!result.getIssueStats().isEmpty()) {
            report.append("\n=== 问题统计 ===\n");
            result.getIssueStats().entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .forEach(entry -> report.append(entry.getKey()).append(": ").append(entry.getValue()).append("个\n"));
        }
        
        return report.toString();
    }
    
    /**
     * 标签验证结果
     */
    public static class TagValidationResult {
        private final boolean valid;
        private final String message;
        private final List<String> issues;
        
        public TagValidationResult(boolean valid, String message, List<String> issues) {
            this.valid = valid;
            this.message = message;
            this.issues = issues;
        }
        
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
        public List<String> getIssues() { return issues; }
    }
    
    /**
     * 批量验证结果
     */
    public static class TagBatchValidationResult {
        private final int totalTopics;
        private final int topicsWithTags;
        private final int validTopics;
        private final Map<String, Integer> issueStats;
        
        public TagBatchValidationResult(int totalTopics, int topicsWithTags, int validTopics, Map<String, Integer> issueStats) {
            this.totalTopics = totalTopics;
            this.topicsWithTags = topicsWithTags;
            this.validTopics = validTopics;
            this.issueStats = issueStats;
        }
        
        public int getTotalTopics() { return totalTopics; }
        public int getTopicsWithTags() { return topicsWithTags; }
        public int getValidTopics() { return validTopics; }
        public Map<String, Integer> getIssueStats() { return issueStats; }
    }
}
