package com.edu.maizi_edu_sys.util;

import com.edu.maizi_edu_sys.entity.Topic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 难度分布验证工具
 * 用于验证组卷结果的难度分布是否符合预期
 */
@Component
@Slf4j
public class DifficultyDistributionValidator {
    
    @Value("${algorithm.genetic.difficulty.easy-max:0.4}")
    private double DIFF_EASY_MAX;
    
    @Value("${algorithm.genetic.difficulty.medium-min:0.5}")
    private double DIFF_MEDIUM_MIN;
    
    @Value("${algorithm.genetic.difficulty.medium-max:0.7}")
    private double DIFF_MEDIUM_MAX;
    
    @Value("${algorithm.genetic.difficulty.hard-min:0.8}")
    private double DIFF_HARD_MIN;
    
    /**
     * 验证题目列表的难度分布
     * @param topics 题目列表
     * @param targetDistribution 目标难度分布
     * @return 验证结果
     */
    public DifficultyValidationResult validateDistribution(List<Topic> topics, Map<String, Double> targetDistribution) {
        if (topics == null || topics.isEmpty()) {
            return new DifficultyValidationResult(false, "题目列表为空", null, null);
        }
        
        // 计算实际分布
        Map<String, Integer> actualCounts = calculateActualDistribution(topics);
        Map<String, Double> actualDistribution = calculatePercentageDistribution(actualCounts, topics.size());
        
        // 计算偏差
        Map<String, Double> deviations = calculateDeviations(actualDistribution, targetDistribution);
        
        // 判断是否通过验证
        boolean isValid = isDistributionValid(deviations);
        
        String message = generateValidationMessage(actualDistribution, targetDistribution, deviations, isValid);
        
        return new DifficultyValidationResult(isValid, message, actualDistribution, deviations);
    }
    
    /**
     * 计算实际难度分布
     */
    private Map<String, Integer> calculateActualDistribution(List<Topic> topics) {
        Map<String, Integer> counts = new HashMap<>();
        counts.put("easy", 0);
        counts.put("medium", 0);
        counts.put("hard", 0);
        
        for (Topic topic : topics) {
            String difficultyName = getDifficultyName(topic.getDifficulty());
            counts.put(difficultyName, counts.get(difficultyName) + 1);
        }
        
        return counts;
    }
    
    /**
     * 计算百分比分布
     */
    private Map<String, Double> calculatePercentageDistribution(Map<String, Integer> counts, int total) {
        Map<String, Double> percentages = new HashMap<>();
        for (Map.Entry<String, Integer> entry : counts.entrySet()) {
            percentages.put(entry.getKey(), (double) entry.getValue() / total);
        }
        return percentages;
    }
    
    /**
     * 计算偏差
     */
    private Map<String, Double> calculateDeviations(Map<String, Double> actual, Map<String, Double> target) {
        Map<String, Double> deviations = new HashMap<>();
        for (String key : target.keySet()) {
            double actualPercent = actual.getOrDefault(key, 0.0);
            double targetPercent = target.get(key);
            deviations.put(key, Math.abs(actualPercent - targetPercent));
        }
        return deviations;
    }
    
    /**
     * 判断分布是否有效（偏差是否在可接受范围内）
     */
    private boolean isDistributionValid(Map<String, Double> deviations) {
        double maxAllowedDeviation = 0.1; // 允许10%的偏差
        for (double deviation : deviations.values()) {
            if (deviation > maxAllowedDeviation) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 生成验证消息
     */
    private String generateValidationMessage(Map<String, Double> actual, Map<String, Double> target, 
                                           Map<String, Double> deviations, boolean isValid) {
        StringBuilder sb = new StringBuilder();
        sb.append("难度分布验证").append(isValid ? "通过" : "失败").append(":\n");
        
        for (String difficulty : target.keySet()) {
            double actualPercent = actual.getOrDefault(difficulty, 0.0);
            double targetPercent = target.get(difficulty);
            double deviation = deviations.get(difficulty);
            
            sb.append(String.format("  %s: 实际=%.1f%%, 目标=%.1f%%, 偏差=%.1f%%\n", 
                    getDifficultyDisplayName(difficulty),
                    actualPercent * 100, 
                    targetPercent * 100, 
                    deviation * 100));
        }
        
        return sb.toString();
    }
    
    /**
     * 获取难度显示名称
     */
    private String getDifficultyDisplayName(String difficulty) {
        switch (difficulty) {
            case "easy": return "简单题";
            case "medium": return "中等题";
            case "hard": return "难题";
            default: return difficulty;
        }
    }
    
    /**
     * 根据数值难度映射到难度名称（与GeneticSolver保持一致）
     */
    private String getDifficultyName(double difficultyValue) {
        // 简单题：<= 0.4
        if (difficultyValue <= DIFF_EASY_MAX) {
            return "easy";
        }
        // 难题：>= 0.8
        if (difficultyValue >= DIFF_HARD_MIN) {
            return "hard";
        }
        // 中等题：0.5-0.7，以及间隙区间的题目
        if (difficultyValue >= DIFF_MEDIUM_MIN && difficultyValue <= DIFF_MEDIUM_MAX) {
            return "medium";
        }
        
        // 处理间隙区间：0.4-0.5 和 0.7-0.8
        if (difficultyValue > DIFF_EASY_MAX && difficultyValue < DIFF_MEDIUM_MIN) {
            double distanceToEasy = difficultyValue - DIFF_EASY_MAX;
            double distanceToMedium = DIFF_MEDIUM_MIN - difficultyValue;
            return distanceToEasy <= distanceToMedium ? "easy" : "medium";
        }
        
        if (difficultyValue > DIFF_MEDIUM_MAX && difficultyValue < DIFF_HARD_MIN) {
            double distanceToMedium = difficultyValue - DIFF_MEDIUM_MAX;
            double distanceToHard = DIFF_HARD_MIN - difficultyValue;
            return distanceToMedium <= distanceToHard ? "medium" : "hard";
        }
        
        return "medium"; // 默认
    }
    
    /**
     * 难度验证结果
     */
    public static class DifficultyValidationResult {
        private final boolean valid;
        private final String message;
        private final Map<String, Double> actualDistribution;
        private final Map<String, Double> deviations;
        
        public DifficultyValidationResult(boolean valid, String message, 
                                        Map<String, Double> actualDistribution, 
                                        Map<String, Double> deviations) {
            this.valid = valid;
            this.message = message;
            this.actualDistribution = actualDistribution;
            this.deviations = deviations;
        }
        
        // Getters
        public boolean isValid() { return valid; }
        public String getMessage() { return message; }
        public Map<String, Double> getActualDistribution() { return actualDistribution; }
        public Map<String, Double> getDeviations() { return deviations; }
    }
}
