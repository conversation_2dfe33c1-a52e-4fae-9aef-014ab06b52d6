package com.edu.maizi_edu_sys.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
@Slf4j
public class RedisConfig {
    
    @Bean(name = "integerRedisTemplate")
    @ConditionalOnMissingBean(name = "integerRedisTemplate")
    public RedisTemplate<String, Integer> integerRedisTemplate(RedisConnectionFactory connectionFactory) {
        try {
            RedisTemplate<String, Integer> template = new RedisTemplate<>();
            template.setConnectionFactory(connectionFactory);

            // 设置key的序列化方式
            template.setKeySerializer(new StringRedisSerializer());
            // 设置value的序列化方式
            template.setValueSerializer(new GenericJackson2JsonRedisSerializer());

            template.setHashKeySerializer(new StringRedisSerializer());
            template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());

            template.afterPropertiesSet();
            log.info("Integer RedisTemplate initialized successfully");
            return template;
        } catch (Exception e) {
            log.error("Failed to initialize Integer RedisTemplate: {}", e.getMessage());
            throw e;
        }
    }

    @Bean(name = "redisTemplate")
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        try {
            RedisTemplate<String, Object> template = new RedisTemplate<>();
            template.setConnectionFactory(connectionFactory);

            // 设置key的序列化方式
            template.setKeySerializer(new StringRedisSerializer());
            // 设置value的序列化方式
            template.setValueSerializer(new GenericJackson2JsonRedisSerializer());

            template.setHashKeySerializer(new StringRedisSerializer());
            template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());

            template.afterPropertiesSet();
            log.info("Object RedisTemplate initialized successfully");
            return template;
        } catch (Exception e) {
            log.error("Failed to initialize Object RedisTemplate: {}", e.getMessage());
            throw e;
        }
    }

    @Bean(name = "byteArrayRedisTemplate")
    @ConditionalOnMissingBean(name = "byteArrayRedisTemplate")
    public RedisTemplate<String, byte[]> byteArrayRedisTemplate(RedisConnectionFactory connectionFactory) {
        try {
            RedisTemplate<String, byte[]> template = new RedisTemplate<>();
            template.setConnectionFactory(connectionFactory);

            // 设置key的序列化方式
            template.setKeySerializer(new StringRedisSerializer());
            // 设置value的序列化方式为字节数组
            template.setValueSerializer(new JdkSerializationRedisSerializer());

            template.setHashKeySerializer(new StringRedisSerializer());
            template.setHashValueSerializer(new JdkSerializationRedisSerializer());

            template.afterPropertiesSet();
            log.info("Byte Array RedisTemplate initialized successfully");
            return template;
        } catch (Exception e) {
            log.error("Failed to initialize Byte Array RedisTemplate: {}", e.getMessage());
            throw e;
        }
    }
} 