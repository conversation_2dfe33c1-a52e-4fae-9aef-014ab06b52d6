package com.edu.maizi_edu_sys.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * 组卷限流配置
 * 使用Redis实现分布式限流，防止组卷接口被恶意调用
 */
@Configuration
@Slf4j
public class PaperGenerationRateLimitConfig {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // 限流Lua脚本
    private static final String RATE_LIMIT_SCRIPT = 
        "local key = KEYS[1]\n" +
        "local limit = tonumber(ARGV[1])\n" +
        "local window = tonumber(ARGV[2])\n" +
        "local current = redis.call('GET', key)\n" +
        "if current == false then\n" +
        "    redis.call('SET', key, 1)\n" +
        "    redis.call('EXPIRE', key, window)\n" +
        "    return 1\n" +
        "else\n" +
        "    if tonumber(current) < limit then\n" +
        "        return redis.call('INCR', key)\n" +
        "    else\n" +
        "        return -1\n" +
        "    end\n" +
        "end";
    
    @Bean
    public DefaultRedisScript<Long> rateLimitScript() {
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(RATE_LIMIT_SCRIPT);
        script.setResultType(Long.class);
        return script;
    }
    
    /**
     * 检查用户是否超过组卷限制
     * @param userId 用户ID
     * @param limit 限制次数
     * @param windowSeconds 时间窗口（秒）
     * @return 是否允许访问
     */
    public boolean isAllowed(Long userId, int limit, int windowSeconds) {
        try {
            String key = "paper_generation_rate_limit:" + userId;
            DefaultRedisScript<Long> script = rateLimitScript();
            
            Long result = redisTemplate.execute(script, 
                Collections.singletonList(key), 
                String.valueOf(limit), 
                String.valueOf(windowSeconds));
            
            if (result != null && result > 0) {
                log.debug("用户{}组卷请求通过，当前计数: {}/{}", userId, result, limit);
                return true;
            } else {
                log.warn("用户{}组卷请求被限流，已达到限制: {}", userId, limit);
                return false;
            }
        } catch (Exception e) {
            log.error("检查组卷限流失败: {}", e.getMessage());
            // 出现异常时允许访问，避免影响正常用户
            return true;
        }
    }
    
    /**
     * 获取用户当前的请求计数
     * @param userId 用户ID
     * @return 当前计数
     */
    public int getCurrentCount(Long userId) {
        try {
            String key = "paper_generation_rate_limit:" + userId;
            Object count = redisTemplate.opsForValue().get(key);
            return count != null ? Integer.parseInt(count.toString()) : 0;
        } catch (Exception e) {
            log.error("获取用户组卷计数失败: {}", e.getMessage());
            return 0;
        }
    }
    
    /**
     * 重置用户的限流计数
     * @param userId 用户ID
     */
    public void resetUserLimit(Long userId) {
        try {
            String key = "paper_generation_rate_limit:" + userId;
            redisTemplate.delete(key);
            log.info("重置用户{}的组卷限流计数", userId);
        } catch (Exception e) {
            log.error("重置用户组卷限流失败: {}", e.getMessage());
        }
    }
}
