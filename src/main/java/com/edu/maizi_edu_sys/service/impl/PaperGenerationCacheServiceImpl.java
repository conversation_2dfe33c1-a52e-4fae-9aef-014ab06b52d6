package com.edu.maizi_edu_sys.service.impl;

import com.edu.maizi_edu_sys.dto.request.PaperGenerationRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.service.PaperGenerationCacheService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 组卷缓存服务实现类
 * 使用Redis缓存组卷结果，提高高并发场景下的性能
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PaperGenerationCacheServiceImpl implements PaperGenerationCacheService {
    
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
    
    private static final String CACHE_PREFIX = "paper_generation:";
    private static final String USER_CACHE_PREFIX = "user_papers:";
    private static final int DEFAULT_EXPIRE_MINUTES = 60; // 默认缓存1小时
    
    @Override
    public String generateCacheKey(PaperGenerationRequest request, Long userId) {
        try {
            // 构建缓存键的核心参数
            StringBuilder keyBuilder = new StringBuilder();
            keyBuilder.append("user:").append(userId);
            keyBuilder.append(":knowledge:").append(request.getKnowledgeIds());
            keyBuilder.append(":total_score:").append(request.getTotalScore());
            keyBuilder.append(":type_counts:").append(request.getTopicTypeCounts());
            keyBuilder.append(":type_scores:").append(request.getTypeScoreMap());
            keyBuilder.append(":difficulty:").append(request.getDifficultyCriteria());
            
            if (request.getCognitiveLevelCriteria() != null) {
                keyBuilder.append(":cognitive:").append(request.getCognitiveLevelCriteria());
            }
            
            if (request.getMinReuseIntervalDays() != null) {
                keyBuilder.append(":reuse_interval:").append(request.getMinReuseIntervalDays());
            }
            
            // 使用MD5生成短的缓存键
            String fullKey = keyBuilder.toString();
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(fullKey.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return CACHE_PREFIX + hexString.toString();
        } catch (Exception e) {
            log.error("生成缓存键失败", e);
            // 如果生成失败，返回一个基于时间的键，确保不会缓存
            return CACHE_PREFIX + "error:" + System.currentTimeMillis();
        }
    }
    
    @Override
    public List<Topic> getCachedPaper(String cacheKey) {
        try {
            Object cached = redisTemplate.opsForValue().get(cacheKey);
            if (cached != null) {
                String jsonStr = cached.toString();
                List<Topic> topics = objectMapper.readValue(jsonStr, new TypeReference<List<Topic>>() {});
                log.debug("从缓存获取组卷结果成功，题目数量: {}", topics.size());
                return topics;
            }
        } catch (Exception e) {
            log.error("从缓存获取组卷结果失败: {}", e.getMessage());
        }
        return null;
    }
    
    @Override
    public void cachePaper(String cacheKey, List<Topic> topics, int expireMinutes) {
        try {
            if (topics == null || topics.isEmpty()) {
                log.debug("题目列表为空，不进行缓存");
                return;
            }
            
            String jsonStr = objectMapper.writeValueAsString(topics);
            redisTemplate.opsForValue().set(cacheKey, jsonStr, expireMinutes, TimeUnit.MINUTES);
            
            log.debug("组卷结果已缓存，键: {}, 题目数量: {}, 过期时间: {}分钟", 
                     cacheKey, topics.size(), expireMinutes);
        } catch (Exception e) {
            log.error("缓存组卷结果失败: {}", e.getMessage());
        }
    }
    
    @Override
    public void clearUserCache(Long userId) {
        try {
            String pattern = CACHE_PREFIX + "*user:" + userId + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清除用户{}的组卷缓存，共{}个键", userId, keys.size());
            }
        } catch (Exception e) {
            log.error("清除用户缓存失败: {}", e.getMessage());
        }
    }
    
    @Override
    public void clearAllCache() {
        try {
            String pattern = CACHE_PREFIX + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清除所有组卷缓存，共{}个键", keys.size());
            }
        } catch (Exception e) {
            log.error("清除所有缓存失败: {}", e.getMessage());
        }
    }
    
    @Override
    public boolean existsCache(String cacheKey) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey));
        } catch (Exception e) {
            log.error("检查缓存存在性失败: {}", e.getMessage());
            return false;
        }
    }
}
