package com.edu.maizi_edu_sys.service;

import com.edu.maizi_edu_sys.dto.request.PaperGenerationRequest;
import com.edu.maizi_edu_sys.entity.Topic;
import java.util.List;

/**
 * 组卷缓存服务接口
 * 用于提高组卷性能，减少重复计算
 */
public interface PaperGenerationCacheService {
    
    /**
     * 根据请求参数生成缓存键
     * @param request 组卷请求
     * @param userId 用户ID
     * @return 缓存键
     */
    String generateCacheKey(PaperGenerationRequest request, Long userId);
    
    /**
     * 从缓存获取组卷结果
     * @param cacheKey 缓存键
     * @return 题目列表，如果缓存不存在则返回null
     */
    List<Topic> getCachedPaper(String cacheKey);
    
    /**
     * 将组卷结果存入缓存
     * @param cacheKey 缓存键
     * @param topics 题目列表
     * @param expireMinutes 过期时间（分钟）
     */
    void cachePaper(String cacheKey, List<Topic> topics, int expireMinutes);
    
    /**
     * 清除用户相关的组卷缓存
     * @param userId 用户ID
     */
    void clearUserCache(Long userId);
    
    /**
     * 清除所有组卷缓存
     */
    void clearAllCache();
    
    /**
     * 检查缓存是否存在
     * @param cacheKey 缓存键
     * @return 是否存在
     */
    boolean existsCache(String cacheKey);
}
