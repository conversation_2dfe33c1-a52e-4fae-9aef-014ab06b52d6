package com.edu.maizi_edu_sys.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * 管理员页面视图控制器
 */
@Controller
@RequestMapping("/admin")
public class AdminViewController {

    /**
     * 管理员首页
     */
    @GetMapping({"/", "/index"})
    public String adminIndex() {
        return "admin/index";
    }

    /**
     * 题目审核页面
     */
    @GetMapping("/topics/audit")
    public String topicAudit() {
        return "admin/topic-audit";
    }

    // 用户管理页面已移至 AdminDashboardController.userManagement()
    // 该方法包含完整的权限验证和错误处理逻辑

    /**
     * 系统统计页面
     */
    @GetMapping("/stats")
    public String systemStats() {
        return "admin/system-stats";
    }

    /**
     * 权限管理页面
     */
    @GetMapping("/permissions")
    public String permissionManagement() {
        return "admin/permission-management";
    }

    /**
     * 管理员登录页面（不需要管理员权限检查）
     */
    @GetMapping("/login")
    public String adminLogin() {
        return "admin/login";
    }

    /**
     * 访问被拒绝页面（不需要管理员权限检查）
     */
    @GetMapping("/access-denied")
    public String accessDenied() {
        return "admin/access-denied";
    }
}
