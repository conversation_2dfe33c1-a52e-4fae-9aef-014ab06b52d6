package com.edu.maizi_edu_sys.controller;

import com.edu.maizi_edu_sys.entity.Topic;
import com.edu.maizi_edu_sys.repository.TopicMapper;
import com.edu.maizi_edu_sys.util.TagDiversityAnalyzer;
import com.edu.maizi_edu_sys.util.TagDataValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签分析控制器
 * 提供标签分布分析和多样性统计功能
 */
@RestController
@RequestMapping("/api/tag-analysis")
@RequiredArgsConstructor
@Slf4j
public class TagAnalysisController {
    
    private final TagDiversityAnalyzer tagDiversityAnalyzer;
    private final TopicMapper topicMapper;
    private final TagDataValidator tagDataValidator;
    
    /**
     * 分析指定知识点的标签分布
     * @param knowId 知识点ID
     * @return 标签分布统计
     */
    @GetMapping("/knowledge-point/{knowId}")
    public Map<String, Object> analyzeKnowledgePointTags(@PathVariable Integer knowId) {
        try {
            List<Topic> topics = topicMapper.selectFromBakByKnowId(knowId);
            
            if (topics.isEmpty()) {
                return createEmptyResponse("No topics found for knowledge point: " + knowId);
            }
            
            TagDiversityAnalyzer.TagDistributionStats stats = tagDiversityAnalyzer.analyzeTagDistribution(topics);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("knowId", knowId);
            response.put("totalTopics", stats.getTotalTopics());
            response.put("topicsWithTags", stats.getTopicsWithTags());
            response.put("tagCoverage", stats.getTagCoverage());
            response.put("diversity", stats.getDiversity());
            response.put("tagCounts", stats.getTagCounts());
            response.put("tagHierarchy", analyzeTagHierarchy(stats.getTagCounts().keySet()));
            
            return response;
        } catch (Exception e) {
            log.error("Error analyzing tags for knowledge point {}: {}", knowId, e.getMessage(), e);
            return createErrorResponse("Failed to analyze tags: " + e.getMessage());
        }
    }
    
    /**
     * 分析题目列表的标签多样性
     * @param request 包含题目ID列表的请求
     * @return 多样性分析结果
     */
    @PostMapping("/diversity")
    public Map<String, Object> analyzeTopicDiversity(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<Integer> topicIds = (List<Integer>) request.get("topicIds");
            
            if (topicIds == null || topicIds.isEmpty()) {
                return createEmptyResponse("No topic IDs provided");
            }
            
            List<Topic> topics = topicMapper.selectBatchIds(topicIds);
            
            if (topics.isEmpty()) {
                return createEmptyResponse("No topics found for provided IDs");
            }
            
            TagDiversityAnalyzer.TagDistributionStats stats = tagDiversityAnalyzer.analyzeTagDistribution(topics);
            boolean tooConcentrated = tagDiversityAnalyzer.isTagDistributionTooConcentrated(topics, 0.5);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("totalTopics", stats.getTotalTopics());
            response.put("topicsWithTags", stats.getTopicsWithTags());
            response.put("tagCoverage", stats.getTagCoverage());
            response.put("diversity", stats.getDiversity());
            response.put("tooConcentrated", tooConcentrated);
            response.put("tagCounts", stats.getTagCounts());
            response.put("concentrationAnalysis", analyzeConcentration(stats.getTagCounts()));
            
            return response;
        } catch (Exception e) {
            log.error("Error analyzing topic diversity: {}", e.getMessage(), e);
            return createErrorResponse("Failed to analyze diversity: " + e.getMessage());
        }
    }
    
    /**
     * 获取标签层级统计
     * @param knowId 知识点ID（可选）
     * @return 标签层级统计
     */
    @GetMapping("/hierarchy")
    public Map<String, Object> getTagHierarchy(@RequestParam(required = false) Integer knowId) {
        try {
            List<Topic> topics;
            if (knowId != null) {
                topics = topicMapper.selectFromBakByKnowId(knowId);
            } else {
                // 获取所有题目的标签（可能需要分页处理大量数据）
                topics = topicMapper.selectList(null);
            }
            
            Set<String> allTags = new HashSet<>();
            for (Topic topic : topics) {
                List<String> tags = tagDiversityAnalyzer.parseTopicTags(topic);
                allTags.addAll(tags);
            }
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("totalTopics", topics.size());
            response.put("totalTags", allTags.size());
            response.put("hierarchy", analyzeTagHierarchy(allTags));
            
            return response;
        } catch (Exception e) {
            log.error("Error getting tag hierarchy: {}", e.getMessage(), e);
            return createErrorResponse("Failed to get tag hierarchy: " + e.getMessage());
        }
    }
    
    /**
     * 分析标签层级结构
     */
    private Map<String, Object> analyzeTagHierarchy(Set<String> tags) {
        Map<String, Set<String>> bookToChapters = new HashMap<>();
        Map<String, Set<String>> chapterToSections = new HashMap<>();
        
        for (String tag : tags) {
            TagDiversityAnalyzer.TagHierarchy hierarchy = tagDiversityAnalyzer.parseTagHierarchy(tag);
            
            if (!hierarchy.getBook().isEmpty()) {
                bookToChapters.computeIfAbsent(hierarchy.getBook(), k -> new HashSet<>())
                             .add(hierarchy.getChapter());
                
                if (!hierarchy.getChapter().isEmpty()) {
                    String chapterKey = hierarchy.getBook() + "-" + hierarchy.getChapter();
                    chapterToSections.computeIfAbsent(chapterKey, k -> new HashSet<>())
                                   .add(hierarchy.getSection());
                }
            }
        }
        
        Map<String, Object> hierarchy = new HashMap<>();
        hierarchy.put("books", bookToChapters.keySet());
        hierarchy.put("bookCount", bookToChapters.size());
        hierarchy.put("chapterCount", bookToChapters.values().stream().mapToInt(Set::size).sum());
        hierarchy.put("sectionCount", chapterToSections.values().stream().mapToInt(Set::size).sum());
        hierarchy.put("bookToChapters", bookToChapters);
        hierarchy.put("chapterToSections", chapterToSections);
        
        return hierarchy;
    }
    
    /**
     * 分析标签集中度
     */
    private Map<String, Object> analyzeConcentration(Map<String, Integer> tagCounts) {
        if (tagCounts.isEmpty()) {
            return Collections.emptyMap();
        }
        
        int totalOccurrences = tagCounts.values().stream().mapToInt(Integer::intValue).sum();
        
        // 找出最频繁的标签
        Map.Entry<String, Integer> mostFrequent = tagCounts.entrySet().stream()
            .max(Map.Entry.comparingByValue())
            .orElse(null);
        
        // 计算前3个最频繁标签的占比
        List<Map.Entry<String, Integer>> sortedTags = tagCounts.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .limit(3)
            .collect(Collectors.toList());
        
        int top3Count = sortedTags.stream().mapToInt(Map.Entry::getValue).sum();
        double top3Ratio = totalOccurrences > 0 ? (double) top3Count / totalOccurrences : 0.0;
        
        Map<String, Object> concentration = new HashMap<>();
        concentration.put("mostFrequentTag", mostFrequent != null ? mostFrequent.getKey() : null);
        concentration.put("mostFrequentCount", mostFrequent != null ? mostFrequent.getValue() : 0);
        concentration.put("mostFrequentRatio", mostFrequent != null && totalOccurrences > 0 ? 
                         (double) mostFrequent.getValue() / totalOccurrences : 0.0);
        concentration.put("top3Tags", sortedTags);
        concentration.put("top3Ratio", top3Ratio);
        concentration.put("totalOccurrences", totalOccurrences);
        
        return concentration;
    }
    
    private Map<String, Object> createEmptyResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", message);
        response.put("totalTopics", 0);
        response.put("diversity", 0.0);
        response.put("tagCounts", Collections.emptyMap());
        return response;
    }
    
    /**
     * 检查标签数据质量
     * @param knowId 知识点ID（可选）
     * @return 数据质量报告
     */
    @GetMapping("/quality-check")
    public Map<String, Object> checkTagDataQuality(@RequestParam(required = false) Integer knowId) {
        try {
            List<Topic> topics;
            if (knowId != null) {
                topics = topicMapper.selectFromBakByKnowId(knowId);
            } else {
                // 为了避免内存问题，限制检查数量
                topics = topicMapper.selectList(null).stream().limit(10000).collect(Collectors.toList());
            }

            TagDataValidator.TagBatchValidationResult result = tagDataValidator.validateTopicListTags(topics);
            String qualityReport = tagDataValidator.generateQualityReport(result);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("knowId", knowId);
            response.put("totalTopics", result.getTotalTopics());
            response.put("topicsWithTags", result.getTopicsWithTags());
            response.put("validTopics", result.getValidTopics());
            response.put("tagCoverage", result.getTotalTopics() > 0 ?
                        (double) result.getTopicsWithTags() / result.getTotalTopics() : 0.0);
            response.put("tagValidityRate", result.getTopicsWithTags() > 0 ?
                        (double) result.getValidTopics() / result.getTopicsWithTags() : 0.0);
            response.put("issueStats", result.getIssueStats());
            response.put("qualityReport", qualityReport);

            return response;
        } catch (Exception e) {
            log.error("Error checking tag data quality: {}", e.getMessage(), e);
            return createErrorResponse("Failed to check tag quality: " + e.getMessage());
        }
    }

    /**
     * 获取有问题的标签示例
     * @param issueType 问题类型（可选）
     * @param limit 返回数量限制
     * @return 问题标签示例
     */
    @GetMapping("/problematic-tags")
    public Map<String, Object> getProblematicTags(
            @RequestParam(required = false) String issueType,
            @RequestParam(defaultValue = "20") int limit) {
        try {
            List<Topic> allTopics = topicMapper.selectList(null).stream().limit(5000).collect(Collectors.toList());
            List<Map<String, Object>> problematicTags = new ArrayList<>();

            for (Topic topic : allTopics) {
                TagDataValidator.TagValidationResult result = tagDataValidator.validateTopicTags(topic);
                if (!result.isValid()) {
                    boolean includeThis = issueType == null ||
                                        result.getIssues().stream().anyMatch(issue -> issue.contains(issueType));

                    if (includeThis) {
                        Map<String, Object> item = new HashMap<>();
                        item.put("topicId", topic.getId());
                        item.put("originalTags", topic.getTags());
                        item.put("fixedTags", tagDataValidator.fixTopicTags(topic));
                        item.put("issues", result.getIssues());
                        problematicTags.add(item);

                        if (problematicTags.size() >= limit) {
                            break;
                        }
                    }
                }
            }

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("issueType", issueType);
            response.put("count", problematicTags.size());
            response.put("problematicTags", problematicTags);

            return response;
        } catch (Exception e) {
            log.error("Error getting problematic tags: {}", e.getMessage(), e);
            return createErrorResponse("Failed to get problematic tags: " + e.getMessage());
        }
    }

    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("error", message);
        return response;
    }
}
